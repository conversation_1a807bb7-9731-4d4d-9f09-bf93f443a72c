apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: memouat
  namespace: argocd
spec:
  description: "Project for memouat environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: memouat 
  roles:
    - name: admin
      policies:
        - p, proj:memouat:admin, applications, *, proj:memouat, allow
        - p, proj:memouat:admin, clusters, *, *, allow
        - p, proj:memouat:admin, repositories, *, *, allow
      groups:
        - admins
