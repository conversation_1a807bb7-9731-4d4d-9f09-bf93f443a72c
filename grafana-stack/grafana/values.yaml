application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 2
    minReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
  environment: grafana-loki
  envFrom: []
  image:
    pullPolicy: IfNotPresent
    repository: grafana/grafana
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: grafana-loki
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 500m
      memory: 1000Mi
      ephemeral-storage: 2Gi
    requests:
      cpu: 300m
      memory: 500Mi
      ephemeral-storage: 1Gi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations: 
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []

loki:
  enabled: true
  image:
    repository: grafana/loki
    tag: latest
  service:
    port: 3100
  persistence:
    enabled: true
    size: 4Gi
  config:
    auth_enabled: false
    ingester:
      chunk_idle_period: 5m
      chunk_retain_period: 30s
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
    storage_config:
      boltdb_shipper:
        active_index_directory: /var/loki/index
        cache_location: /var/loki/cache
        shared_store: filesystem

promtail:
  enabled: true
  image:
    repository: grafana/promtail
    tag: latest
  config:
    clients:
      - url: http://loki:3100/loki/api/v1/push
    positions:
      filename: /var/log/positions.yaml
    scrape_configs:
      - job_name: system
        static_configs:
          - targets:
              - localhost
            labels:
              job: varlogs
              __path__: /var/log/*log

grafana:
  admin:
    existingSecret: grafana-secrets
    userKey: admin-user
    passwordKey: admin-password

ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: grafana
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: grafana-loki.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80

presync:
  args: []
  command: []
  enabled: false



# replicas: 2
# global:
#   imagePullSecrets:
#     - docker-secret
# nodeSelector:
#   eks.amazonaws.com/nodegroup: redis
# tolerations:
#   - key: "where"
#     operator: "Equal"
#     value: "redis"
#     effect: "NoSchedule"
# persistence:
#   type: pvc
#   enabled: true
#   # storageClassName: default
#   accessModes:
#     - ReadWriteOnce
#   size: 30Gi
#   # annotations: {}
#   finalizers:
#     - kubernetes.io/pvc-protection

# # adminUser: admin
# # adminPassword: strongpassword