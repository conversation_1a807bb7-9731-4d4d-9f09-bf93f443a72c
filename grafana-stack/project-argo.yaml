apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: grafana-stack
  namespace: argocd
spec:
  description: "Project for grafana-stack environment"
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: grafana-stack 
  roles:
    - name: admin
      policies:
        - p, proj:grafana-stack:admin, applications, *, proj:grafana-stack, allow
        - p, proj:grafana-stack:admin, clusters, *, *, allow
        - p, proj:grafana-stack:admin, repositories, *, *, allow
      groups:
        - admins
