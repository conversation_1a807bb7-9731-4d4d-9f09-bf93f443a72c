apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: kong
  namespace: argocd
spec:
  description: "Project for kong environment"
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: kong 
    - server: 'https://kubernetes.default.svc'
      namespace: '*'
  roles:
    - name: admin
      policies:
        - p, proj:kong:admin, applications, *, proj:kong, allow
        - p, proj:kong:admin, clusters, *, *, allow
        - p, proj:kong:admin, repositories, *, *, allow
      groups:
        - admins
