image:
  repository: kong/kong-gateway
  tag: "3.9"

secretVolumes:
- kong-cluster-certdev

admin:
  enabled: false

env:
  role: data_plane
  database: "off"
  cluster_mtls: pki
  cluster_control_plane: 1afd10716b.us.cp0.konghq.com:443
  cluster_server_name: 1afd10716b.us.cp0.konghq.com
  cluster_telemetry_endpoint: 1afd10716b.us.tp0.konghq.com:443
  cluster_telemetry_server_name: 1afd10716b.us.tp0.konghq.com
  cluster_cert: /etc/secrets/kong-cluster-certdev/tls.crt
  cluster_cert_key: /etc/secrets/kong-cluster-certdev/tls.key
  lua_ssl_trusted_certificate: system
  konnect_mode: "on"
  vitals: "off"
  nginx_worker_processes: "1"
  upstream_keepalive_max_requests: "100000"
  nginx_http_keepalive_requests: "100000"
  proxy_access_log: "off"
  dns_stale_ttl: "3600"

ingressController:
  enabled: false
  installCRDs: false

resources:
  requests:
    cpu: 1
    memory: "2Gi"