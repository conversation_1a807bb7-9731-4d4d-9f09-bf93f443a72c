application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: "http://accountingservice"
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    API_GATEWAY: "http://apigateway"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "http://certificates"
    DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    HOST: "https://website-mobile-qa.dev.nivodaapi.net/"
    KEYCLOAK_ADMIN_SERVER_URL: "http://keycloak/auth"
    KEYCLOAK_SERVER_URL: "http://keycloak/auth"
    MARKET_PAY_NIVODA_SERVICE: "http://marketpay"
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_SQL_LOGGING: "false"
    NODE_ENV: "production"
    PGSSLMODE: "no-verify"
    REDIS_TLS_URL: "rediss://redis-master.mobile-qa.svc.cluster.local:6379"
    REDIS_URL: "redis://redis-master.mobile-qa.svc.cluster.local:6379"
    S3_IMAGE_BUCKET: "nivoda-staging"
    SERVER_HOST: "https://website-mobile-qa.dev.nivodaapi.net"
    SLACK_OUTGOING: "*****************************************************************************"
    TAYLOR_HART_WEBHOOK_URL: "http://demo7656881.mockable.io/taylor"
    TWILIO_RATE_LIMIT_IDENTIFIER: "end_user_phone_number"
    USE_NETSUITE: "true"
    USE_XERO_DEMO: "true"
    WDC_ENV: "staging"
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: "website-mobile-qa.dev.nivodaapi.net"
    port: "4002"
    DB_CONNECTION_READ_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DB_CONNECTION_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    EXPIRATION_TIME: "60"
    KRIYA_ADMIN: "<EMAIL>"
    HMAC_SECRET_KEY_AU: "W3xZF3gosdyv6Bp*kKgiWnTnGFdBkzac2dgdUJEa"
    HMAC_SECRET_KEY_GB: "ftkkxTkx!CiRcxG*wGZmqsZjJeeG_B7iitmk74eU"
    integration_service_admin: "http://integrations/graphql-admin"
    integration_service: "http://integrations/graphql"
    allowed_cors: "https://porter-app-dev.nivodaapi.net,https://credit-staging.nivoda.net,credit-staging.nivoda.net,alpha.nivoda.net,feature.nivoda.net,*"
    MARKET_PAY_WEBHOOK_TOKEN: "jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c="
    MARKET_PAY_SECURITY_TOKEN: "jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c="
    MARKET_PAY_ENDPOINT_V2: "https://api.kriya.dev/payments"
    MARKET_PAY_API_KEY_US: "427649f616394b9081d8fcd0b907efff"
    MARKET_PAY_API_KEY_GB: "872faec619c94d8a8b363902693a557a"
    MARKET_PAY_API_KEY_NL: "a7b09e354ea5462db330c121e476f802"
    MARKET_PAY_API_KEY_BE: "e81fd7c8f3c94954ad1a1f161eb570e6"
    MARKET_PAY_API_KEY_AU: "5f0779db070f45949b37e60659634bc2"
    ADMIN_USER: "<EMAIL>"
    ADMIN_PASS: "Nivoda123"
    REDIS_URI_BULL: "redis-master.mobile-qa.svc.cluster.local:6379"
    QUEUE_ENABLED: "true"
  environment: mobile-qa
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/marketpay
    tag: 1_BR_20241227-1149_d2c380c
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v2/market_pay/health_check
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: marketpay
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v2/market_pay/health_check
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4002
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: mobile-qa
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: marketpay-mobile-qa.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
