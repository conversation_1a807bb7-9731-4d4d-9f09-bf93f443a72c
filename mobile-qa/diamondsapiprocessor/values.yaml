application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    REDIS_URL: "redis://redis-master.mobile-qa.svc.cluster.local:6379"
    PLATFORM_URL: "http://integrations/graphql-admin"
    DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    SLACK_SEC_TOKEN: "****************************************************************************"
    PROXY_URL: "*************"
    RUN_INITIALLY: "true"
    S3_KEY: "ASIAZG7RPP7YACHQFFF6"
    S3_SECRET: "bvF5GsMumZl5BrNku7HuIYy94I3hXeW2BDDOSrFI"
    CERTIFICATE_HOST: "http://certificates"
    PROD_DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    SAVE_UNMAPPED_VALUES: "true"
    NODE_ENV: "staging"
    WDC_ENV: "staging"
    DELTA_PROCESSING: "false"
    DELTA_PROCESSING_VERSION_2: "true"
    S3_SUPPLIER_FILES_BUCKET: "nivoda-staging"
    QUEUE_ENABLED: "false"
    REDIS_URI_BULL: "redis://redis-master.mobile-qa.svc.cluster.local:6379"
    HISTORIC_STONES_QUEUE: "HISTORIC_STONES_MAIN_QUEUE"
    DELTA_PROCESSING_FOR_ALL_SUPPLIER: "true"
    SQS_QUEUE_URL: "https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo"
    AWS_REGION: "eu-west-1"
    ADMIN_USER: "<EMAIL>"
    ADMIN_PASS: "Nivoda123"
    GRAPHQL_INTERNAL_TOKEN: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7hOrbE_-1lbO3Je34JZjQihZ1X_vJmyx_bHLI-mIjlQ"
  environment: mobile-qa
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/diamondsapiprocessor
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3004
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: diamondsapiprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3004
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3004
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: mobile-qa
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: diamondsapiprocessor-mobile-qa.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
