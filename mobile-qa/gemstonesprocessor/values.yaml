application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    PORT: "3005"
    NODE_ENV: "staging"
    CERTIFICATE_HOST: "http://certificates"
    PROCESS_API_STOCK_FREQUENCY_HOURS: "2"
    CONCURRENT_SUPPLIERS: "1"
    ENABLE_CRON: "false"
    REDIS_URL: "redis://redis-master.mobile-qa.svc.cluster.local:6379"
    PLATFORM_URL: "http://integrations/graphql-admin"
    DATABASE_URL: "postgresql://integrations:<EMAIL>/gemstones_processor"
    SLACK_TOKEN: "asdfasdf"
    S3_IMAGE_BUCKET: "dev-nivoda-staging"
    GRAPHQL_INTERNAL_TOKEN: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g_g8ry4MUJHzN0kDm96QCAajW13kMexGoCCcMJHc_zA"
  environment: mobile-qa
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/gemstonesprocessor
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health 
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: gemstonesprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health 
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3005
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: mobile-qa
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: gemstonesprocessor-mobile-qa.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
