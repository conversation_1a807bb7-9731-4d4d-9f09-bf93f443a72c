application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    KC_CACHE: ispn
    KC_CACHE_STACK: kubernetes
    KC_HTTP_HOST: "0.0.0.0"
    KC_PROXY: "edge" 
    KC_<PERSON>: "postgres"
    KC_DB_URL: "***************************************************************************"
    KC_DB_USERNAME: "keycloak"
    KC_DB_PASSWORD: "rQrlEC17pboABB4vKNuB"
    KC_HOSTNAME: "keycloak-mobile-qa.dev.nivodaapi.net"
    KC_HOSTNAME_STRICT: "false"
    KC_HOSTNAME_STRICT_HTTPS: "false"
    KC_LOG_LEVEL: "info"
    KC_METRICS_ENABLED: "false"
    KC_HEALTH_ENABLED: "false"
    KC_HTTP_ENABLED: "true"
    QUARKUS_TRANSACTION_MANAGER_ENABLE: "false"
    QUARKUS_DATASOURCE_JDBC_TRANSACTIONS: "enabled"
    QUARKUS_DATASOURCE_JDBC_ENABLE_XA_TRANSACTION: "false"
    KC_DB_SCHEMA_UPDATE: 'false'
    KC_CLUSTER_DISCOVERY: 'dns'
    JGROUPS_DISCOVERY_PROTOCOL: 'dns.DNS_PING'
    JGROUPS_DISCOVERY_PROPERTIES: 'dns_query=keycloak-headless-service.coreui.svc.cluster.local'
    JAVA_OPTS_APPEND: '-Djgroups.dns.query=keycloak-headless-service.coreui.svc.cluster.local'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: mobile-qa
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/dev/keycloak24
    tag: "v1"
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 8080
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: keycloak
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 8080
    initialDelaySeconds: 300
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 3
  resources:
    limits:
      cpu: 300m
      memory: 2000Mi
    requests:
      cpu: 200m
      memory: 1200Mi
  securityContext: {}
  service:
    port: 8080
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: 
  - name: config-volume
    mountPath: "/tmp/conf/"
    readOnly: true
  volumes: 
  - name: config-volume
    configMap:
      name: keycloak-config
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: mobile-qa
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: keycloak-mobile-qa.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: true
