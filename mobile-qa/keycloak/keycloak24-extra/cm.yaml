apiVersion: v1
kind: ConfigMap
metadata:
  name: keycloak-config
  namespace: feeds
data:
  quarkus.properties: |-
    quarkus.datasource.user-store.db-kind=postgresql
    quarkus.datasource.user-store.username=integrations
    quarkus.datasource.user-store.password=rQrlEC17pboABB4vKNuB
    quarkus.datasource.user-store.jdbc.url=***********************************************************
    quarkus.datasource.user-store.jdbc.transactions=disabled
    quarkus.datasource.user-store.jdbc.driver=org.postgresql.Driver
    quarkus.datasource.user-store.enabled=false
    quarkus.datasource.user-store.devservices=false
    quarkus.datasource.user-store.jdbc.min-size=2
    quarkus.datasource.user-store.jdbc.max-size=20
    quarkus.datasource.user-store.jdbc.transaction-requirement=off