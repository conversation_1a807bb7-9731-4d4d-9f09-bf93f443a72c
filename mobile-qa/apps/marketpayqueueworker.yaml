apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: marketpayqueueworker-mobile-qa
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v4
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/mobile-qa/marketpayqueueworker/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'mobile-qa'
  project: mobile-qa