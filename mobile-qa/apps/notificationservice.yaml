apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: notificationservice-mobile-qa
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: web-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: web-chart
      targetRevision: v6
      helm:
        releaseName: web-chart
        valueFiles:
          - $valuesRepo/mobile-qa/notificationservice/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'mobile-qa'
  project: mobile-qa