apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: credits
  namespace: argocd
spec:
  description: "Project for credits environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: credits 
  roles:
    - name: admin
      policies:
        - p, proj:credits:admin, applications, *, proj:credits, allow
        - p, proj:credits:admin, clusters, *, *, allow
        - p, proj:credits:admin, repositories, *, *, allow
      groups:
        - admins
