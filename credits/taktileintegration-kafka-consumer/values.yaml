application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node bin/lib/kafka.js
  env:
    DB_TARGET: taktile
    ENVIRONMENT: sandbox
    INTEGRATIONS_DB_DATABASE: credits
    INTEGRATIONS_DB_HOST: db-writer-fintech.dev.nivodaapi.net
    INTEGRATIONS_DB_PASSWORD: rQrlEC17pboABB4vKNuB
    INTEGRATIONS_DB_USERNAME: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: taktile-integrations
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: transaction-monitoring-events
    KAFKA_USERNAME: nivoda
    LOG_LEVEL: db
    NODE_ENV: staging
    ONBOARDING_WORKFLOW_ENTITY_ID: taktile_onb_001
    PORT: '3006'
    TAKTILE_API_KEY: def7345a-2cbf-4756-a28e-7986436743c6
    TAKTILE_AUTH_SECRET: b72f4d1e-3c91-4895-9c1d-0a84e7d7f29a
    TAKTILE_BASE_URL: https://risk.3dddc027.decide.taktile.com
    TAKTILE_DB_DATABASE: taktile
    TAKTILE_DB_HOST: db-writer-fintech.dev.nivodaapi.net
    TAKTILE_DB_PASSWORD: rQrlEC17pboABB4vKNuB
    TAKTILE_DB_USERNAME: integrations
    TAKTILE_UNDERWRITING_DATASET_ID: c7df04e0-f193-4235-b099-f433881dd08f
    TAKTILE_UNDERWRITING_FLOW_ID: e095a658-5967-476b-ad53-3673eddb466f
    TAKTILE_UNDERWRITING_JOB_ID: 50dd55d3-b8e9-4435-9ca4-a76b976ce11c
    UNDERWRITING_WORKFLOW_ENTITY_ID: taktile_uw_001
    USER_RISK_PROFILE_WORKFLOW_ENTITY_ID: taktile_urpw_001
    WINSTON_TO_FILE: 'true'
    allowed_cors: http://localhost:3001,http://localhost:4200,https://website-credits2.nivodaapi.net,https://website-credits.nivodaapi.net,https://website-credits.dev.nivodaapi.net,https://website-finops.dev.nivodaapi.net,https://app.taktile.com,https://website-beta.dev.nivodaapi.net,*
  environment: credits
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/taktileintegration
    tag: 1_BR_20250729-1541_56b7c60
  imagePullSecrets: []
  labels: {}
  name: taktileintegration-kafka-consumer
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
scaledObject:
  enabled: false
