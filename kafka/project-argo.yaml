apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: kafka
  namespace: argocd
spec:
  description: "Project for kafka environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: kafka 
  roles:
    - name: admin
      policies:
        - p, proj:kafka:admin, applications, *, proj:kafka, allow
        - p, proj:kafka:admin, clusters, *, *, allow
        - p, proj:kafka:admin, repositories, *, *, allow
      groups:
        - admins
