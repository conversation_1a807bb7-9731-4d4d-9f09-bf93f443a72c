apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kafka
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: kafka
    - repoURL: "oci://registry-1.docker.io/bitnamicharts/kafka"
      targetRevision: "31.1.1"
      chart: kafka
      helm:
        releaseName: kafka
        valueFiles:
          - $valuesRepo/kafka/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'kafka'
  project: kafka