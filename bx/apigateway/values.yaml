application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    allowed_cors: "https://webite-bx.dev.nivodaapi.net"
    feature_flag_user_token: "dhjdh*dufhd@k2d_2dgv21dwqwnd363@dmlldhe?erj.skelenbfbf8k738"
    feature_flagging_admin_token: "*:*.ad59ae274908f169a21e3252d9054e5b4fcca3ad1088ea4642a74200"
    feature_flagging_api_key: "*:development.ce3c78188eaa0d063a3f21e6d68cb47627fd9e133ab1ddadef8fc7a6"
    feature_flagging_appname: "default"
    feature_flagging_dv2_strategy_url: "https://unleash-bx.dev.nivodaapi.net/api/admin/projects/default/features/diamonds-v2/environments/development/strategies/3054108f-417a-4f46-a28d-d9f6e35010b4"
    feature_flagging_env: "development"
    feature_flagging_url: "http://unleash/api"
    jwt_cert: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAogZMHwjmaXZCU71bVvuHelVjF1I60ujB8wRKBQrSyy0VcreyLOaMV8FIuSU5Ktk2smO97rq0xL6WAJFlsvZqRu7bWy4mS2FfqSLtGrApnFUrxHmN9z9eymGD9dVPJmiVmO6RoNqq4UP+FFP0wEwZerN8pKREGrBs99i/L8IczPaZSyh+Tp/orz/Re/erLqV5y8k+BuAtKhHNOst9bU3XizX77gFkjD00JdIcF9NWlMaB7KVaN9QNa0NaXPSIRIXhYrsUSlpvrnEN3+fVnAWIJ8guRawN5PwRre3WE3Ba/Xrc7KG3EP0Kn6RBuXN3ZUkKFcu/64AVkQ4+nylHyeUktwIDAQAB"
    jwt_secret: "secretjaleukoke"
    marketpay_url: "http://marketpay/"
    feedsapi_base_url: "http://feedsapi/"
    monolith_base_url: "http://integrations/"
    translationapi_url: "http://translations"
    port: "4000"
  environment: bx
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/apigateway
    tag: 1_BR_20241222-1757_b6568e6
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 4000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: apigateway
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 4000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: bx
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: apigateway-bx.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
