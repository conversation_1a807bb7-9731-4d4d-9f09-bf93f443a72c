apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: jewellery-sku-add-message-kafka-consumer-jewellery
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v6
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/jewellery/jewellery-sku-add-message-kafka-consumer/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'jewellery'
  project: jewellery