apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: jewellery
  namespace: argocd
spec:
  description: "Project for jewellery environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: jewellery 
  roles:
    - name: admin
      policies:
        - p, proj:jewellery:admin, applications, *, proj:jewellery, allow
        - p, proj:jewellery:admin, clusters, *, *, allow
        - p, proj:jewellery:admin, repositories, *, *, allow
      groups:
        - admins
