# terraform {
#   before_hook "before_hook" {
#     commands     = ["apply", "plan"]
#     execute      = ["tfsec", "."]
#   }
# }

# locals {
#   # Automatically load environment-level variables
#   environment_vars = read_terragrunt_config(find_in_parent_folders("env.hcl"))
# }

remote_state {
    backend = "s3"
    generate = {
        path = "state.tf"
        if_exists = "overwrite_terragrunt"
    }
    config = {
        # profile = "stag"
        # role_arn = "arn: aws: iam:: 424432388155: role/terraform"
        bucket = "dev-nivoda-infra-state"
        key = "internalops/${path_relative_to_include()}/terraform.tfstate"
        region = "eu-west-2"
        encrypt = true
        dynamodb_table = "terraform-lock-table"
    }
}

generate "provider" {
    path = "provider.tf"
    if_exists = "overwrite_terragrunt"
    contents = <<EOF
        provider "aws" {
            region = "eu-west-2"
            
        }
    EOF
}

# assume_role {
#                 session_name = "leson-160"
#                 role_arn = "arn: aws: iam:: 424432388155: role/terraform"
#             }