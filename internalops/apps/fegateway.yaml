apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fegateway-internalops
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: web-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: web-chart
      targetRevision: v4
      helm:
        releaseName: web-chart
        valueFiles:
          - $valuesRepo/internalops/fegateway/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'internalops'
  project: internalops