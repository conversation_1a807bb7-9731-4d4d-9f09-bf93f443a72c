apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: internalops
  namespace: argocd
spec:
  description: "Project for internalops environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: internalops 
  roles:
    - name: admin
      policies:
        - p, proj:internalops:admin, applications, *, proj:internalops, allow
        - p, proj:internalops:admin, clusters, *, *, allow
        - p, proj:internalops:admin, repositories, *, *, allow
      groups:
        - admins
