application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ELASTICSEARCH_HOST: https://vpc-searchdb-d2pkjlzhcsni47fmf7n5udg66q.eu-west-2.es.amazonaws.com/
    ELASTICSEARCH_KEEPALIVE_SEC: '30'
    ELASTICSEARCH_MAX_SOCKET: '100'
    ELASTICSEARCH_PASSWORD: null
    ELASTICSEARCH_USERNAME: null
    LOG_LEVEL: debug
    NODE_ENV: local
    PORT: '3333'
    PRIMARY_INDEX: diamonds_doc_internalops
    SCROLL_CONTEXT_TIME: 1m
    WDC_ENV: development
  environment: internalops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/searchservice
    tag: 1_BR_20250716-1912_050b1a3
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /system/healthcheck
      port: 3333
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: searchservice
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /system/healthcheck
      port: 3333
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 3000Mi
    requests:
      cpu: 100m
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3333
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: internalops
    alb.ingress.kubernetes.io/healthcheck-path: /system/healthcheck
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: searchservice-internalops.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
