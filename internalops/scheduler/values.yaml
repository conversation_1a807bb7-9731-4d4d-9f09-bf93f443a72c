application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node scheduler.js
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_EMAIL: <EMAIL>
    AIRWALLEX_NIVODA_SERVICE: http://airwallex
    ALLIANZ_NIVODA_SERVICE: http://allianz
    ALLIANZ_POLICY_ID: '*********'
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_API_KEY: test
    APOLLO_ENGINE_ENABLED: 'true'
    CERTIFICATE_HOST: http://certificates
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    CREDIT_SAFE_PASSWORD: k!em140V7$45e3i!_;ktM9
    CREDIT_SAFE_URL: https://connect.sandbox.creditsafe.com/v1
    CREDIT_SAFE_USERNAME: <EMAIL>
    DATABASE_URL: postgresql://integrations:<EMAIL>/internalops
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/internalops
    DB_URL: postgresql://integrations:<EMAIL>/internalops
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/internalops
    HOST: https://website-internalops.dev.nivodaapi.net/
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_MAIN_EVENT: NivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_SUPPLIER_CENTRAL_TOPIC: SupplierCentralInternalOps
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_INVOICES_BUCKET: nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: nivoda-staging
    NODE_ENV: staging
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_SERVICE_NAME: integrations-internalops
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    POSTMEN_TEST_KEY: c9bc93a5-7934-471b-ab1c-37b262363892
    QUEUE_ENABLED: 'true'
    REDIS_TLS_URL: rediss://redis-master.internalops.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.internalops.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.internalops.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-internalops.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-internalops.dev.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
    run_migration: 'true'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: internalops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250113-1733_85e9e9050
  imagePullSecrets: []
  labels: {}
  name: scheduler
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 3000Mi
    requests:
      cpu: 100m
      memory: 3000Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
