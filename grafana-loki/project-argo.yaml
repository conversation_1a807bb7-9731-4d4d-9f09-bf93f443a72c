apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: grafana-loki
  namespace: argocd
spec:
  description: "Project for grafana-loki environment"
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: grafana-loki 
  roles:
    - name: admin
      policies:
        - p, proj:grafana-loki:admin, applications, *, proj:grafana-loki, allow
        - p, proj:grafana-loki:admin, clusters, *, *, allow
        - p, proj:grafana-loki:admin, repositories, *, *, allow
      groups:
        - admins
