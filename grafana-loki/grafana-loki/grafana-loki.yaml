apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: grafana-loki
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: grafana
    - repoURL: "https://grafana.github.io/helm-charts"
      targetRevision: "8.9.0"
      chart: grafana
      helm:
        releaseName: grafana
        valueFiles:
          - $valuesRepo/grafana-loki/grafana-loki/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'grafana-loki'
  project: grafana-loki