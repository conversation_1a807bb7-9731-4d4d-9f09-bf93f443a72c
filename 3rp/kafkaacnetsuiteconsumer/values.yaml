application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  args:
  - kafka/kafkaNetsuiteConsumer.js
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command:
  - node
  env:
    ACCOUNTING_NETSUITE_URL: https://accountingservice-3rp.dev.nivodaapi.net
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_NIVODA_SERVICE: https://airwallex-3rp.nivodaapi.net
    ALLIANZ_NIVODA_SERVICE: https://allianz-3rp.nivodaapi.net
    ALLIANZ_POLICY_ID: '*********'
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    APOLLO_ENGINE_ENABLED: 'true'
    BASIC_ATH_PASSWORD: Temp/ms123
    BASIC_AUTH_USER: salman
    CERTIFICATE_HOST: https://certificates-3rp.nivodaapi.net
    CERT_REMOVAL_EVENTS: 'true'
    CONSUMERKEY: fcae0edc911091e22429601b75b9a686af697cddae96c75ef59806c97ee00ca9
    CONSUMERSECRET: 3a37214ee14787569564a1b87a8d12ab8c7a0d104aa2ea21b3556f34e6af1383
    CREDIT_SAFE_CREDENTIALS: '{password:OXLY66kH-Q(ZIQRFM@Q5, username:<EMAIL>}'
    DATABASE_URL: postgresql://integrations:<EMAIL>/3rp
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/3rp
    DB_URL: postgresql://integrations:<EMAIL>/3rp
    DIAMONDS_SERVICE_STAGING: https://diamonds-3rp.nivodaapi.net
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/3rp
    GEMSTONES_SERVICE_STAGING: https://gemstonesprocessor-3rp.nivodaapi.net
    HOST: https://website-3rp.nivodaapi.net/
    INTEGRATION_URL: https://integrations-3rp.dev.nivodaapi.net
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CLIENT_ID: accouting_client-app
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: accounting-client
    KAFKA_HIGH_PRIORITY_EVENT: HighPriorityEvents
    KAFKA_MAIN_EVENT: NivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: NetsuiteEvents
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-3rp.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-3rp.nivodaapi.net/auth
    MARKET_PAY_NIVODA_SERVICE: https://marketpay-3rp.nivodaapi.net
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    NETSUITE_PROD_URL: https://8631227.suitetalk.api.netsuite.com
    NETSUITE_SB_URL: https://8631227-sb1.suitetalk.api.netsuite.com
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: nivoda-staging
    NIVODA_EXPORTS_BUCKET: nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: nivoda-staging
    NIVODA_IMAGES_BUCKET: nivoda-staging
    NIVODA_INSURANCE_BUCKET: nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: nivoda-staging
    NODE_ENV: staging
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_SERVICE_NAME: kafkaacnetsuiteconsumer-3rp
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    POSTMEN_TEST_KEY: c9bc93a5-7934-471b-ab1c-37b262363892
    POSTMEN_TEST_REGION: sandbox
    REALM: 8631227_SB1
    REDIS_TLS_URL: redis://redis-master.3rp.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.3rp.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.3rp.svc.cluster.local:6379
    REDSHIFT_DATABASE: bi_server
    REDSHIFT_HOST: ************
    REDSHIFT_PASSWORD: ahmed@1KNuB
    REDSHIFT_PORT: '5439'
    REDSHIFT_USER: ahmed
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-3rp.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    STOCK_REMOVAL_EVENTS: 'true'
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TOKENKEY: ab403439aed4bcf54f0d37281580cd080e3a52aea18bdd5a8aabe3499e99903e
    TOKENSECRET: 2f0cce1486b79f6da63d6edc20b679b4d9f0476f3e6388546840003973eee4b6
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USER_PROFILES_BUCKET: nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-3rp.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  environment: 3rp
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/accountingservice
    tag: 1_BR_20250603-1852_b9ed1e3
  imagePullSecrets: []
  labels: {}
  name: kafkaacnetsuiteconsumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
