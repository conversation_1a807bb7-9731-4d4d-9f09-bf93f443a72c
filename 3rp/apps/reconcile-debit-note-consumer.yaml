apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: reconcile-debit-note-consumer-3rp
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v6
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/3rp/reconcile-debit-note-consumer/values.yaml
  destination:
    name: 'in-cluster'
    namespace: '3rp'
  project: 3rp