application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    EXPOSE_SWAGGER_API: "false"
    KAFKA_ACCESS_KEY: "********************"
    KAFKA_SECRET_ACCESS_KEY: "/Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7"
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_PRODUCR_TOPIC: SD-historic-diamonds
    KAFKA_GROUP_ID: historic-diamonds
    DATABASE_URL:  "postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5"
    PORT: "4445"
    NODE_ENV: "staging"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_CONSUMER_TOPIC: "testtopic"
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKAJS_NO_PARTITIONER_WARNING: "1"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/historicdataservice
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 4445
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 4445
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4445
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
