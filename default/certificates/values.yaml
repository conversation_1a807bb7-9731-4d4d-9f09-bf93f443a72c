application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    EXPOSE_SWAGGER_API: "true"
    DATABASE_URL:  "postgresql://integrations:<EMAIL>/certificates"
    PORT: "4444"
    NODE_ENV: "staging"
    IGI_VERSION_2: "true"
    HRD_VERSION_2: "true"
    GCAL_VERSION_2: "true"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_PRODUCER_TOPIC: "testtopic"
    KAFKA_CONSUMER_TOPIC: "testtopic"
    KAFKA_ACCESS_KEY: "********************"
    KAFKA_SECRET_ACCESS_KEY: "/Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7"
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
    KAFKAJS_NO_PARTITIONER_WARNING: "1"
    S3_BUCKET: "nivoda-masked-pdfs"
    S3_SECRET: "2Dmpb0D8L8JFyTiQFJmM8WFLQ3uXwxnTyCnU3Nx1"
    HOST: "0.0.0.0"
    SQS_CERTIFICATE_QUEUE_URL: "https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo"
    JWT_SECRET: "3a5fada2cec90122dff179e7d51ab8deab5b70a1d7f5605348cb87f7b9d4ef82c4350ae14a5952e9a9691ace1a93e441e1d6d7d7096a72687af4ea7d86353105"
    USER: "Nivoda"
    PASS: "Nivoda123"
    HRD_API_TOKEN: "b72ea4e6-30c1-4fb5-a8c6-bbcaa4d203cf"
    GSI_API_TOKEN: "9ED5DC41FA184AAB8458B5DADAF07643"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/certificates
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 4444
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 4444
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4444
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
