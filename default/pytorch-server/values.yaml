application:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: node.kubernetes.io/instance-type
            operator: NotIn
            values:
            - spot
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    enabled: false
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: []
  args: []
  env:
    HOST: 0.0.0.0
    PORT: 8080
    MODEL_PATH: /models
    DEVICE: cuda
    BATCH_SIZE: 1
    MAX_WORKERS: 4
    LOG_LEVEL: INFO
    ENABLE_METRICS: "true"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/pytorch-server
    tag: latest
  imagePullSecrets: []
  labels:
    app: pytorch-server
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 120
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 10
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /ready
      port: 8080
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 5
  replicaCount: 1
  resources:
    limits:
      cpu: 2000m
      memory: 8Gi
      nvidia.com/gpu: 1
    requests:
      cpu: 1000m
      memory: 4Gi
      nvidia.com/gpu: 1
  securityContext: {}
  service:
    port: 8080
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations:
  - key: nvidia.com/gpu
    operator: Exists
    effect: NoSchedule
  volumeMounts: []
  volumes: []
  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: "nodegroup"
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app: pytorch-server
scaledObject:
  enabled: false 