application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    NODE_ENV: "staging"
    DB_CONNECTION_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    DB_CONNECTION_READ_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    db_ssl: "false"
    REALM: "8631227_SB1"
    NETSUITE_SB_URL: "https://8631227-sb1.suitetalk.api.netsuite.com"
    NETSUITE_PROD_URL: "https://8631227.suitetalk.api.netsuite.com"
    CLEARTAX_SB_URL: "https://api-sandbox.clear.in/einv/v2"
    CLEARTAX_SB_AUTH_TOKEN: "1.674100fd-7219-486d-b8f1-5c7f8814e0c1_2cb41405bdd96c19d5451d60c018daaf039c00056552d617949cf684acf67760"
    CONSUMERKEY: "****************************************************************"
    CONSUMERSECRET: "****************************************************************"
    TOKENKEY: "****************************************************************"
    TOKENSECRET: "c0ee0c30eb08d2e71859c3332c5d23ba2f7b70ccf4b88236c5da40235d917fe6"
    TO_FILE: "false"
    REST_LET_URL: "https://8631227.restlets.api.netsuite.com/app/site/hosting/restlet.nl"
    REST_LET_SCRIPT: "1010"
    port: "4002"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/accountingservice
    tag: 1_BR_20241127-1629_8cb7294
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /getNetsuiteHealthCheck
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /getNetsuiteHealthCheck
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4002
    type: ClusterIP
  serviceaccount:
    annotations: 
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
