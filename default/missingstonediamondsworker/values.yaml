application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/queue_worker_missing_stones.js
  env:
    REDIS_URL: "redis://redis-master.${environment}.svc.cluster.local:6379"
    PLATFORM_URL: "http://integrations/graphql-admin"
    DATABASE_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    SLACK_SEC_TOKEN: "****************************************************************************"
    PROXY_URL: "*************"
    RUN_INITIALLY: "true"
    S3_KEY: "ASIAZG7RPP7YACHQFFF6"
    S3_SECRET: "bvF5GsMumZl5BrNku7HuIYy94I3hXeW2BDDOSrFI"
    CERTIFICATE_HOST: "http://certificates"
    PROD_DATABASE_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    SAVE_UNMAPPED_VALUES: "true"
    NODE_ENV: "staging"
    WDC_ENV: "staging"
    DELTA_PROCESSING: "false"
    DELTA_PROCESSING_VERSION_2: "true"
    S3_SUPPLIER_FILES_BUCKET: "nivoda-staging"
    QUEUE_ENABLED: "false"
    REDIS_URI_BULL: "redis://redis-master.${environment}.svc.cluster.local:6379"
    HISTORIC_STONES_QUEUE: "HISTORIC_STONES_MAIN_QUEUE"
    DELTA_PROCESSING_FOR_ALL_SUPPLIER: "true"
    SQS_QUEUE_URL: "https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo"
    AWS_REGION: "eu-west-1"
    ADMIN_USER: "<EMAIL>"
    ADMIN_PASS: "Nivoda123"
    GRAPHQL_INTERNAL_TOKEN: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7hOrbE_-1lbO3Je34JZjQihZ1X_vJmyx_bHLI-mIjlQ"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/diamonds
    tag: 1_BR_20241220-1436_8009c4bf
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false