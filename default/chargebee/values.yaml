application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 3
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    integration_service: http://host.docker.internal:5000/graphql-admin
    graphql_service: http://host.docker.internal:5000/graphql
    # if you change this this will need to be changed in the docker-compose aswell
    port: "4002"
    DB_CONNECTION_URL: postgresql://postgres:<EMAIL>:5433/nivoda
    ignore_permissions: "true"
    NODE_ENV: development
    allowed_cors: http://localhost:3001,http://localhost:4000,*
    DB_CONNECTION_READ_URL: postgresql://postgres:<EMAIL>:5433/nivoda
    db_pool:  '{"max": 10,"min": 0,"idle": 5000,"acquire": 50000,"handleDisconnects": true}'
    REDIS_URI_BULL: redis://host.docker.internal:6379
    QUEUE_ENABLED: "false"
    KAFKA_ENABLED: "true"
    KAFKA_GROUP_ID: integrations
    KAFKA_TOPIC: NivodaEvents
    KAFKA_AUTHORIZATION_IDENTITY: "123"
    KAFKA_ACCESS_KEY: "123"
    KAFKA_SECRET_ACCESS_KEY: "123"
    KAFKA_USERNAME: "123"
    KAFKA_PASSWORD: "123"
    KAFKA_CLIENT_ID: integrations-app
    KAFKA_BROKER: host.docker.internal:29092
    CHARGEBEE_SITE: nivoda-subscriptions-test
    CHARGEBEE_API_KEY: test_vcd37xaXAT7fawATvKSATQhkD2kKY5QHOs
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/chargebee
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /chargebee/health
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /chargebee/health
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4002
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
