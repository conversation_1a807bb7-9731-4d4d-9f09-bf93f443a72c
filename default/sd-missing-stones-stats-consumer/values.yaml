application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    API_GATEWAY: "http://apigateway"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "http://certificates"
    DATABASE_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    HOST: "https://website-${environment}.nivodaapi.net/"
    KEYCLOAK_ADMIN_SERVER_URL: "http://keycloak/auth"
    KEYCLOAK_SERVER_URL: "http://keycloak/auth"
    MARKET_PAY_NIVODA_SERVICE: "http://marketpay"
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_SQL_LOGGING: "false"
    NODE_ENV: "production"
    PGSSLMODE: "no-verify"
    PORT: "3000"
    REDIS_TLS_URL: rediss://redis-master.${environment}.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.${environment}.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.${environment}.svc.cluster.local:6379
    S3_IMAGE_BUCKET: nivoda-staging
    SERVER_HOST: https://website-${environment}.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_NETSUITE: "false"
    USE_XERO_DEMO: "true"
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: website-${environment}.dev.nivodaapi.net
    pool_max: "500"
    pool_acquire: "30000"
    ALLIANZ_NIVODA_SERVICE: http://allianz
    AIRWALLEX_NIVODA_SERVICE: http://airwallex
    ALLIANZ_POLICY_ID: '291417601'
    idle_in_transaction_session_timeout: "400000"
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_USERNAME: nivoda
    KAFKA_PASSWORD: nivoda123
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_MAIN_EVENT: 'NivodaEvents'
    KAFKA_HIGH_PRIORITY_EVENT: 'HighPriorityEvents'
    KAFKA_URGENT_PRIORITY_EVENT: 'UrgentNivodaEvents'
    KAFKAJS_NO_PARTITIONER_WARNING: "1"
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    DB_CONNECTION_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    DIAMONDS_SERVICE_STAGING: "http://diamonds"
    GEMSTONES_SERVICE_STAGING: "http://gemstonesprocessor"
    ENCRYPTION_KEY: "774B191AF2D84AE6B802D2AC49AA9BFF"
    STOCK_REMOVAL_EVENTS: "true"
    CERT_REMOVAL_EVENTS: "true"
    KAFKA_GROUP_ID: "supplier-data-stock-removal-app"
  environment: ${environment}
  command: node observers/StockConsumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20241224-2007_383c25127
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  