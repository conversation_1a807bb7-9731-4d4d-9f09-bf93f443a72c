application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    USE_NETSUITE: 'false'
    ACCOUNTING_NETSUITE_URL: https://accountingservice-credits.nivodaapi.net
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    APOLLO_ENGINE_ENABLED: 'true'
    CERTIFICATE_HOST: https://certificates-credits.nivodaapi.net
    DATABASE_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    HOST: https://website-credits2.nivodaapi.net
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-credits.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-credits.nivodaapi.net/auth
    MARKET_PAY_NIVODA_SERVICE: https://marketpay-credits2.nivodaapi.net
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_SQL_LOGGING: 'false'
    NODE_ENV: production
    run_migration: 'true'
    PGSSLMODE: no-verify
    PORT: '3000'
    REDIS_TLS_URL: 'rediss://redis-master.${environment}.svc.cluster.local:6379'
    REDIS_URL: 'redis://redis-master.${environment}.svc.cluster.local:6379'
    REDIS_URI_BULL: 'redis://redis-master.${environment}.svc.cluster.local:6379'
    S3_IMAGE_BUCKET: nivoda-staging
    SERVER_HOST: https://website-${environment}.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    QUEUE_ENABLED: 'true'
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-${environment}.dev.nivodaapi.net
    pool_max: '500'
    pool_acquire: '30000'
    ALLIANZ_NIVODA_SERVICE: https://allianz-credits.nivodaapi.net
    AIRWALLEX_NIVODA_SERVICE: https://airwallex-credits2.nivodaapi.net
    ALLIANZ_POLICY_ID: '291417601'
    idle_in_transaction_session_timeout: '400000'
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_USERNAME: nivoda
    KAFKA_PASSWORD: nivoda123
    KAFKA_ENABLED: 'true'
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/credits2
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    SEON_BASE_URL: https://api.seon.io/SeonRestService
    SEON_API_KEY: 1ebbefec-ae3d-4e6f-9dcb-0e3e6b185a54
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/seonintegration
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: "3000"
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: "3000"
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 3000Mi
    requests:
      cpu: 100m
      memory: 3000Mi
  securityContext: {}
  service:
    port: "3000"
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
