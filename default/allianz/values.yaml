application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: "http://accountingservice"
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    API_GATEWAY: "http://apigateway"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "http://certificates"
    DB_CONNECTION_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    HOST: "https://website-${environment}.dev.nivodaapi.net/"
    KEYCLOAK_ADMIN_SERVER_URL: "http://keycloak/auth"
    KEYCLOAK_SERVER_URL: "http://keycloak/auth"
    MARKET_PAY_NIVODA_SERVICE: "http://marketpay"
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_SQL_LOGGING: "false"
    NODE_ENV: "staging"
    PGSSLMODE: "no-verify"
    port: "4008"
    REDIS_TLS_URL: "rediss://redis-master.${environment}.svc.cluster.local:6379"
    REDIS_URL: "redis://redis-master.${environment}.svc.cluster.local:6379"
    S3_IMAGE_BUCKET: "nivoda-staging"
    SERVER_HOST: "https://website-${environment}.dev.nivodaapi.net"
    SLACK_OUTGOING: "*****************************************************************************"
    TAYLOR_HART_WEBHOOK_URL: "http://demo7656881.mockable.io/taylor"
    TWILIO_RATE_LIMIT_IDENTIFIER: "end_user_phone_number"
    USE_NETSUITE: "true"
    USE_XERO_DEMO: "true"
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: "website-${environment}.dev.nivodaapi.net"
    pool_max: "500"
    pool_acquire: "30000"
    idle_in_transaction_session_timeout: "400000"
    ALLIANZ_BASE_URL: "https://api-services.uat.1placedessaisons.com"
    ALLIANZ_API_KEY: "************************************************************************"
    COMPANY_PREFIX: "uatm-"
    COVER_PREFIX: "/uatm/riskinfo/v3/"
    TRADE_PREFIX: "/uatm/"
    AUTH_PREFIX: "/uatm/v1/idp/oauth2/authorize"
    ignore_permissions: "true"
    db_pool: '{"max": 10,"min": 0,"idle": 5000,"acquire": 50000,"handleDisconnects": "true"}'
    integration_service: "http://integrations/graphql-admin"
    graphql_service: "http://integrations/graphql"
    allowed_cors: "https://porter-app-dev.nivodaapi.net,https://credit-staging.nivoda.net,credit-staging.nivoda.net,alpha.nivoda.net,feature.nivoda.net,*"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/allianz
    tag: 1_BR_20241220-1348_d0e726f
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/allianz/health_check
      port: 4008
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/allianz/health_check
      port: 4008
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4008
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
