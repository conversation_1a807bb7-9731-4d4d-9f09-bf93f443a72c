application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    PORT: '3005'
    NODE_ENV: development
    LOG_LEVEL: db
    DB_HOST: db-writer-bx.dev.nivodaapi.net
    DB_DATABASE: ${environment}
    DB_PORT: '5433'
    DB_USERNAME: postgres
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_INTEGRATIONS_HOST: db-writer-bx.dev.nivodaapi.net
    DB_INTEGRATIONS_DATABASE: ${environment}
    DB_INTEGRATIONS_PORT: '5433'
    DB_INTEGRATIONS_USERNAME: postgres
    DB_INTEGRATIONS_PASSWORD: rQrlEC17pboABB4vKNuB
    KAFKA_BROKER: 'b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096'
    KAFKA_USERNAME: 'nivoda'
    KAFKA_PASSWORD: 'nivoda123'
    KAFKA_ENABLED: 'true'
    KAFKA_SASL_MECHANISM: 'SCRAM-SHA-512'
    KAFKA_SSL: 'enabled'
    KAFKA_AUTHORIZATION_IDENTITY: 'integrations'
    INTEGRATIONS_BASE_URL: http://localhost:3000
    INTEGRATIONS_USERNAME: testname
    INTEGRATIONS_PASSWORD: '123456'
  environment: ${environment}
  command: node devops/kafka/Consumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/notifications
    tag: 1_BR_20241224-2007_383c25127
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  