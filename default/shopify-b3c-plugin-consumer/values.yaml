application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DATABASE_URL: postgresql://integrations:<EMAIL>/b2c-plugin-shopify
    EXCLUDE_SUPPLIER_IDS: 4c2b578b-da7c-4627-a6d0-d03ddf406aaa,e961d9bf-d682-401e-a789-2614a95805dc,89274cb4-c11a-4d43-b01b-3ec74de0c6c4
    MONETIZATION_ACTIVE: 'true'
    PORT: '8081'
    SCOPES: read_content,read_metaobject_definitions,read_metaobjects,read_orders,read_publications,read_script_tags,read_themes,unauthenticated_read_product_listings,write_content,write_files,write_metaobject_definitions,write_metaobjects,write_products,write_publications,write_script_tags,write_themes,write_fulfillments,read_locations,write_locations,read_assigned_fulfillment_orders,write_assigned_fulfillment_orders,write_third_party_fulfillment_orders
    SENTRY_NODE_ENV: staging
    SHOPIFY_API_KEY: d8abf38d35813c5a7051df1ac3a92d0c
    SHOPIFY_API_SECRET: 6b634107413e3487206ab1c69e73e5e9
    SHOPIFY_APP_URL: https://b2c-plugin-shopify-core-be.dev.nivodaapi.net/
    SHOPIFY_FEEDS_API: http://integrations/graphql-loupe360
    SHOPIFY_ORDERS_API: http://integrations/graphql-shopify
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: true
    KAFKA_GROUP_ID: shopify-app
    NODE_ENV: development
    KAFKA_MAIN_EVENT: NivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_USERNAME: nivoda
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
  environment: ${environment}
  command: node app/kafka-consumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20241224-2007_383c25127
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  