application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    enabled: false
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DB_HOST: database-memouat.dev.nivodaapi.net
    DB_PORT: "5432"
    DB_DATABASE: d6ruvqcqduhvn5
    DB_USERNAME: postgres
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    NODE_ENV: staging
    PORT: "4001"
    DB_NAME: wise
    KAFKA_ENABLED: "true"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_MAIN_EVENT: WiseEvents
  environment: ${environment}
  command: node app/kafka-consumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/wise-certificates
    tag: lastest
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  