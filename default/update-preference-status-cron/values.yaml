application:
  name: update-preference-status-cron
  schedule: "*/20 * * * *"
  command: node bin/cronJobs/updatePreferenceStatusCron.js
  resources:
    limits:
      cpu: 100m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 512Mi
  failedJobsHistoryLimit: 1
  successfulJobsHistoryLimit: 3
  concurrencyPolicy: Forbid
  restartPolicy: Never
  labels: {}
  affinity: {}
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    CRON_ENABLED: 'true'
    DB_DATABASE: suppliercentral
    DB_DATABASE_SC_V2: d6ruvqcqduhvn5
    DB_HOST: db-writer-sx.dev.nivodaapi.net
    DB_HOST_SC_V2: db-writer-sx.dev.nivodaapi.net
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_PASSWORD_SC_V2: rQrlEC17pboABB4vKNuB
    DB_PORT: '5432'
    DB_PORT_SC_V2: '5432'
    DB_SQL_LOGGING: 'true'
    DB_USERNAME: integrations
    DB_USERNAME_SC_V2: integrations
    DEBUG: development
    JWT_SECRET: secretjaleukoke
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'false'
    KAFKA_GROUP_ID: supplier-central
    KAFKA_MAIN_EVENT: NivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: SupplierCentralInternalOps
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    OPEN_SEARCH_PASSWORD: ''
    OPEN_SEARCH_URL: https://vpc-searchdb-d2pkjlzhcsni47fmf7n5udg66q.eu-west-2.es.amazonaws.com
    OPEN_SEARCH_USERNAME: ''
    PORT: '3005'
    SUPPLIER_CENTRAL_BASE_URL: https://suppliercentral-sx.dev.nivodaapi.net
    WINSTON_TO_FILE: 'true'
    ZENDESK_API_KEY: 3425cd5751442adee7c5c25ceb00330680692d06f34b977562d37fe47d555893
    ZENDESK_BASE_URL: https://nivodahelp.zendesk.com/
  environment: ${environment}
  envFrom: 
  - secretRef:
      name: common-secrets
  imagePullSecrets: []
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  tolerations: []
  serviceaccount:
    annotations: 
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/suppliercentral
    tag: 1_BR_20241224-2007_383c25127

