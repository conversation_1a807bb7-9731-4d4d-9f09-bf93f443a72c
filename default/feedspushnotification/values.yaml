application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    POSTGRES_PASSWORD: rQrlEC17pboABB4vKNuB
    SQLALCHEMY_DATABASE_URI: postgresql://postgres:<EMAIL>/feeds-data
    INTEGRATIONS_DB_URI: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    AUTO_OFFSET_RESET: earliest
    BOOTSTRAP_SERVERS_PRIV: b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    BOOTSTRAP_SERVERS_PUB: b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    CONSUMER_GROUP: testFeedService
    CONSUMER_TOPIC: test
    ENABLE_AUTO_COMMIT: "false"
    MAX_POLL_INTERVAL_MS: "600000"
    MAX_POLL_RECORDS: "100"
    sasl_mechanism_priv: SCRAM-SHA-512
    sasl_mechanism_pub: SCRAM-SHA-512
    sasl_plain_username_pub: nivoda
    sasl_plain_password_pub: nivoda123
    sasl_plain_username_priv: nivoda
    sasl_plain_password_priv: nivoda123m n6y
    FEEDS_API_HOST: "http://feedsapi"
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/feeds
    tag: latest
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  