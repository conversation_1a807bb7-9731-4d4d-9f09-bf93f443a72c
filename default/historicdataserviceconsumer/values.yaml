application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_ACCESS_KEY: ********************
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: SD-stock-updates
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: historic-diamonds-sx
    KAFKA_TOPIC: SD-stock-updates
    KAFKA_PASSWORD: nivoda123
    KAFKA_PRODUCR_TOPIC: SD-historic-diamonds
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SECRET_ACCESS_KEY: /Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    PORT: '4445'
    HOST: historicdataservice-sx.dev.nivodaapi.net
  environment: ${environment}
  command: node kafka/consumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/historicdataservice
    tag: 1_BR_20241224-2007_383c25127
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  