application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 3
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    #url for the payment integrations service
    integration_service: "http://host.docker.internal:5000/graphql-admin"
    graphql_service: "http://host.docker.internal:5000/graphql"
    # if you change this this will need to be changed in the docker-compose aswell
    port: "4002"
    DB_CONNECTION_URL: "postgresql://postgres:<EMAIL>:5433/nivoda"
    ignore_permissions: "true"
    NODE_ENV: "development"
    allowed_cors: "http://localhost:3001,http://localhost:4000,*"
    DB_CONNECTION_READ_URL: "postgresql://postgres:<EMAIL>:5433/nivoda"
    db_pool: '{"max": 10,"min": 0,"idle": 5000,"acquire": 50000,"handleDisconnects": true}'
    REDIS_URI_BULL: "redis://host.docker.internal:6379"
    QUEUE_ENABLED: "false"
    KAFKA_ENABLED: "true"
    KAFKA_ACCESS_KEY: "********************"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_CLIENT_ID: "accouting_client-app"
    KAFKA_GROUP_ID: "integrations-sx"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SECRET_ACCESS_KEY: "/Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7"
    KAFKA_SSL: "enabled"
    KAFKA_TOPIC: "NivodaEvents"
    KAFKA_USERNAME: "nivoda"
    CHARGEBEE_SITE: "nivoda-subscriptions-test"
    CHARGEBEE_API_KEY: "test_vcd37xaXAT7fawATvKSATQhkD2kKY5QHOs"
  environment: ${environment}
  command: node kafka/ChargeebeeConsumer.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/chargebee
    tag: latest
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  