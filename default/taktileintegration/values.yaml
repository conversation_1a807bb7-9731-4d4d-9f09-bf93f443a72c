application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    PORT: "3006"
    NODE_ENV: "development"
    LOG_LEVEL: "db"
    allowed_cors: https://website-${environment}.nivodaapi.net,https://website-${environment}.nivodaapi.net,https://app.taktile.com,*
    #database: variables
    DB_HOST: "database-${environment}.dev.nivodaapi.net"
    DB_DATABASE: "d6ruvqcqduhvn5"
    DB_PORT: "5432"
    DB_USERNAME: "postgres"
    DB_PASSWORD: "rQrlEC17pboABB4vKNuB"
    #winston: variables
    WINSTON_TO_FILE: "true"
    TAKTILE_BASE_URL: "https://risk.3dddc027.decide.taktile.com/run/api/v1/flows/underwriting-flow/sandbox"
    TAKTILE_API_KEY: "86316e10-baf6-4409-a24e-37846afc3ee4"
    UNDERWRITING_WORKFLOW_ENTITY_ID: "taktile_uw_001"
    TAKTILE_AUTH_SECRET: b72f4d1e-3c91-4895-9c1d-0a84e7d7f29a
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/taktileintegration
    tag: 1_BR_20241219-1357_6b7f0b3
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: "3006"
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: "3006"
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 3000Mi
    requests:
      cpu: 100m
      memory: 3000Mi
  securityContext: {}
  service:
    port: "3006"
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: ${environment}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: ${service}-${environment}.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
