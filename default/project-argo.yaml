apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ${environment}
  namespace: argocd
spec:
  description: "Project for ${environment} environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: ${environment} 
  roles:
    - name: admin
      policies:
        - p, proj:${environment}:admin, applications, *, proj:${environment}, allow
        - p, proj:${environment}:admin, clusters, *, *, allow
        - p, proj:${environment}:admin, repositories, *, *, allow
      groups:
        - admins
