apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: redis-${environment}
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  project: ${environment} 
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: redis
    - repoURL: "https://charts.bitnami.com/bitnami"
      targetRevision: "20.1.4"
      chart: redis
      helm:
        releaseName: redis
        valueFiles:
          - $valuesRepo/${environment}/redis/values.yaml
  destination:
    name: 'in-cluster'
    namespace: ${environment}     