apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: remove-media-from-certificates-cron-${environment}
  namespace: argocd
spec:
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: job-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: job-chart
      targetRevision: v6
      helm:
        releaseName: job-chart
        valueFiles:
          - $valuesRepo/${environment}/remove-media-from-certificates-cron/values.yaml
  destination:
    name: 'in-cluster'
    namespace: '${environment}'
  project: ${environment}
  syncPolicy: 
    automated: 
      prune: true
      selfHeal: true 
      