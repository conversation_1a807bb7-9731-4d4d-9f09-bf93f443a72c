application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    IMAGE_BUCKET: internal-test-2023
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/676625059805/nivoda-videos-new.fifo
    PLATFORM_URL: http://integrations/graphql-admin
    MAX_MESSAGES_COUNT: "2"
    KAFKA_BROKER: b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    USE_KAFKA: "true"
    KAFKA_GROUP_ID: feedconsumer
    KAFKA_USERNAME: nivoda
    KAFKA_PASSWORD: nivoda123
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    RUN_FFMPEG_INSTALLER: "false"
    HOST: "0.0.0.0"
    KAFKA_CONSUMER_TOPIC: feedspush
    DBCONNECTIONURL: postgresql://postgres:<EMAIL>:5432/feeds-data
    DBCONNECTIONREADURL: postgresql://postgres:<EMAIL>:5432/feeds-data
    DBCONNECTIONINTEGRATIONSURL: postgresql://postgres:rQrlEC17pboABB4vKNuB@database-${environment}.dev.nivodaapi.net/d6ruvqcqduhvn5
    ENVIRONMENT: STAGING
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/feedsapi
    tag: latest
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  