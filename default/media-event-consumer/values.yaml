application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/observers/MediaEventConsumer.js
  env:
    DB_HOST: database-memouat.dev.nivodaapi.net
    DB_PORT: "5432"
    DB_DATABASE: d6ruvqcqduhvn5
    DB_USERNAME: postgres
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    KAFKA_GROUP_ID: wise-consumer
    KAFKA_CONSUMER_TOPIC: WiseMediaEvents
  environment: ${environment}
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/wise-certificates
    tag: latest
  imagePullSecrets: []
  labels: {}
  name: ${service}
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
