application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: https://accountingservice-finops.dev.nivodaapi.net
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_EMAIL: <EMAIL>
    AIRWALLEX_NIVODA_SERVICE: https://airwallex-finops.dev.nivodaapi.net
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    APOLLO_ENGINE_API_KEY: test
    APOLLO_ENGINE_ENABLED: 'true'
    AUTH_URL: https://fegateway-finops.dev.nivodaapi.net
    CERTIFICATE_HOST: https://certificates-finops.nivodaapi.net
    DATABASE_URL: postgresql://integrations:<EMAIL>/finops
    DB_URL: postgresql://integrations:<EMAIL>/finops
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    HOST: https://website-finops.nivodaapi.net/
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CLIENT_ID: create-pf-kafka-consumer-finops
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: create-pf-kafka-consumer-finops
    KAFKA_HIGH_PRIORITY_EVENT: HighPriorityEvents
    KAFKA_MAIN_EVENT: Reconcile-CreditNote
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: Reconcile-CreditNote
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-finops.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-finops.nivodaapi.net/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: production
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    POST_CODE_API_URL: https://v0.postcodeapi.com.au/
    QUEUE_ENABLED: 'true'
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-finops.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TOKENKEY: ab403439aed4bcf54f0d37281580cd080e3a52aea18bdd5a8aabe3499e99903e
    TOKENSECRET: 2f0cce1486b79f6da63d6edc20b679b4d9f0476f3e6388546840003973eee4b6
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_SID: **********************************
    TWILIO_TOKEN: 3ec4e8a4cad679bb3545b5adbe782e9c
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'true'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    ZENDESK_JWT_API_KEY: app_674d433e337dd8ccfa323cce
    ZENDESK_JWT_SECRET: GaKOgqXMCejR-4aBcP9tK37N8nWKMVAdGFaofa-Rn1ZgG7KBaf3fSENCvf_a3aonZqClfOR4cJlNqyUrNy7uoA
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/finops
    GOOGLE_CLIENT_EMAIL: <EMAIL>
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

      '
    NIVODA_SUPPLIER_COMPANY_ID: 8c9c022e-365e-46c0-bd28-f30bdb1ce138
    OTEL_SERVICE_NAME: integrations-finops
    REDIS_TLS_URL: redis://redis-master.finops.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.finops.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.finops.svc.cluster.local:6379
    SQL_STATEMENT_TIMEOUT: '180000'
    STRIPE_EMAIL: <EMAIL>
    WS_HOST: website-finops.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    okta_client_id: 0oakqh8k926XUNwHR5d7
    okta_issuer: https://dev-32165027.okta.com/oauth2/default
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: finops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250729-1127_cb8ceda5e9
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: integrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 300m
      ephemeral-storage: 2Gi
      memory: 3000Mi
    requests:
      cpu: 300m
      ephemeral-storage: 1Gi
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: finops
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: integrations-finops.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: false
