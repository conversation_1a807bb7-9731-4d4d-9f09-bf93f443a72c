apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: finops
  namespace: argocd
spec:
  description: "Project for finops environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: finops 
  roles:
    - name: admin
      policies:
        - p, proj:finops:admin, applications, *, proj:finops, allow
        - p, proj:finops:admin, clusters, *, *, allow
        - p, proj:finops:admin, repositories, *, *, allow
      groups:
        - admins
