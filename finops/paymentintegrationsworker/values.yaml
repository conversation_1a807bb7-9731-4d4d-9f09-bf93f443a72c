application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  args:
  - /app/workers/queue_worker.js
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command:
  - node
  env:
    ACCOUNTING_NETSUITE_URL: https://accounting-service-3197a576c6503f9f.onporter.run
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    ADMIN_PASS: Nivoda123
    ADMIN_USER: <EMAIL>
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    APOLLO_ENGINE_ENABLED: 'true'
    CERTIFICATE_HOST: https://certificates-qa.nivodaapi.net
    CREDIT_CHARGE: '2.5'
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/credits2
    DB_URL: postgresql://integrations:<EMAIL>/credits2
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/qa
    HOST: https://website-qa.nivodaapi.net/
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-qa.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-qa.nivodaapi.net/auth
    MARKET_PAY_NIVODA_SERVICE: https://porter-marketpay-dev.nivodaapi.net
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_SQL_LOGGING: 'false'
    NODE_ENV: production
    PGSSLMODE: no-verify
    QUEUE_ENABLED: 'true'
    REDIS_TLS_URL: redis-master.credits.svc.cluster.local:6379
    REDIS_URI_BULL: redis-master.credits.svc.cluster.local:6379
    REDIS_URL: redis-master.credits.svc.cluster.local:6379
    S3_IMAGE_BUCKET: nivoda-staging
    SERVER_HOST: https://website-qa.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    STRIPE_AUTH_KEY: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    STRIPE_SECRET_KEY_AUD: sk_test_51MTu2wGUaRmKzM5u71f3mvvDhpRyeAGT6HlSVc1Yn5ydUjCWB4b1ZKD7eWlzaWslbvzXWRePvkXWg64uD7mCmGox00LFE7D7e9
    STRIPE_SECRET_KEY_EUR: sk_test_51MP4ykDi100pd0KfVbN8HbnIFe3LZR6uHUc8XSw2dFUCVrazhBm22LZssM8BWjjGVaxSmZXPYjgoCZ6Y43QCghnW00DJaKEZOb
    STRIPE_SECRET_KEY_UK: sk_test_51Cwt0FJ1nXM93VFF8pV0UAV8mDPbWZWFcMXFMONl42OFtzke8qOGIkgBBDUoextFV43nm2UO1pwS0v5atwD6mVWQ00xquVt1lE
    STRIPE_SECRET_KEY_US: sk_test_51LzdVWEHMBNJiNpatUen6W4KoXEzDRHh44i9jZK4s8vyawz4QxDuIIVm18SQJG6gtw1aq0HkLAfJ0CV1FdRY05Jr00Za3oljWm
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_NETSUITE: 'true'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-qa.nivodaapi.net
    allowed_cors: '*'
    graphql_service: https://integrations-credits2.nivodaapi.net/graphql
    idle_in_transaction_session_timeout: '400000'
    ignore_permissions: 'true'
    integration_service: https://integrations-credits2.nivodaapi.net/graphql-admin
    pool_acquire: '30000'
    pool_max: '500'
    port: '4002'
  environment: finops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/paymentintegrations
    tag: 1_BR_20250707-1522_469a1c2
  imagePullSecrets: []
  labels: {}
  name: paymentintegrationsworker
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
