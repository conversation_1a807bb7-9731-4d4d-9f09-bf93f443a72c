application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    CLEARTAX_SB_AUTH_TOKEN: 1.674100fd-7219-486d-b8f1-5c7f8814e0c1_2cb41405bdd96c19d5451d60c018daaf039c00056552d617949cf684acf67760
    CLEARTAX_SB_URL: https://api-sandbox.clear.in/einv/v2
    CONSUMERKEY: ****************************************************************
    CONSUMERSECRET: ****************************************************************
    DB_CONNECTION_READ_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DB_CONNECTION_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    NETSUITE_PROD_URL: https://8631227.suitetalk.api.netsuite.com
    NETSUITE_SB_URL: https://8631227-sb1.suitetalk.api.netsuite.com
    NODE_ENV: staging
    REALM: 8631227_SB1
    REST_LET_SCRIPT: '1010'
    REST_LET_URL: https://8631227.restlets.api.netsuite.com/app/site/hosting/restlet.nl
    TOKENKEY: ****************************************************************
    TOKENSECRET: 2f0cce1486b79f6da63d6edc20b679b4d9f0476f3e6388546840003973eee4b6
    TO_FILE: 'false'
    db_ssl: 'false'
    port: '4002'
  environment: finops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/accountingservice
    tag: 1_BR_20250725-1735_956b15c
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /getNetsuiteHealthCheck
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: accountingservice
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /getNetsuiteHealthCheck
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4002
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: finops
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: accountingservice-finops.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
