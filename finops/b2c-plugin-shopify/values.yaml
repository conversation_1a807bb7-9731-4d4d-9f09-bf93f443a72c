application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DATABASE_URL: postgresql://integrations:<EMAIL>/b2c-plugin-shopify
    PORT: '8081'
    SCOPES: read_content,read_metaobject_definitions,read_metaobjects,read_orders,read_publications,read_script_tags,read_themes,unauthenticated_read_product_listings,write_content,write_files,write_metaobject_definitions,write_metaobjects,write_products,write_publications,write_script_tags,write_themes
    SENTRY_NODE_ENV: staging
    SHOPIFY_API_KEY: d8abf38d35813c5a7051df1ac3a92d0c
    SHOPIFY_API_SECRET: 6b634107413e3487206ab1c69e73e5e9
    SHOPIFY_APP_URL: https://b2c-plugin-shopify-finops.dev.nivodaapi.net/
  environment: finops
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/b2c-plugin-shopify
    tag: 1_BR_20241213-1028_3e1b5b4
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/health
      port: 8081
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: b2c-plugin-shopify
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/health
      port: 8081
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 8081
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: finops
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: b2c-plugin-shopify-finops.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
