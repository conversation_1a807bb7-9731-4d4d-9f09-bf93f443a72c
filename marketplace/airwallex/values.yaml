application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ADMIN_PASS: "Nivoda123"
    ADMIN_USER: "<EMAIL>"
    AIRWALLEX_ACCOUNT_ID_AU: "acct_8cD5-vwMN0qKsQVs2xuCKw"
    AIRWALLEX_ACCOUNT_ID_BE: "acct_8cD5-vwMN0qKsQVs2xuCKw"
    AIRWALLEX_ACCOUNT_ID_NL: "acct_8cD5-vwMN0qKsQVs2xuCKw"
    AIRWALLEX_ACCOUNT_ID_UK: "acct_8cD5-vwMN0qKsQVs2xuCKw"
    AIRWALLEX_ACCOUNT_ID_US: "acct_8cD5-vwMN0qKsQVs2xuCKw"
    AIRWALLEX_API_KEY: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_API_KEY_AU: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_API_KEY_BE: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_API_KEY_NL: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_API_KEY_US: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_API_KEY_HK: "e1703fd9ab90cfe330180c2db2cc72dbe9a95a04dc03101c1056bd4d9835d0a2dddb4b880c84108ef5af413394c64488"
    AIRWALLEX_BASE_URL: "https://api-demo.airwallex.com/api/v1"
    AIRWALLEX_CLIENT_ID: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_CLIENT_ID_AU: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_CLIENT_ID_BE: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_CLIENT_ID_NL: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_CLIENT_ID_US: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_CLIENT_ID_HK: "E5OXLWVgTrWGAjINu7lXaA"
    AIRWALLEX_WEBHOOK_SECRET: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_AU: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_BE: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_NL: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_UK: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_US: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    AIRWALLEX_WEBHOOK_SECRET_HK: "whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ"
    CREDIT_CHARGE: "2.5"
    DB_CONNECTION_READ_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    NODE_ENV: "development"
    port: "4006"
    QUEUE_ENABLED: "true"
    REDIS_URI_BULL: "redis-master.marketplace.svc.cluster.local:6379"
    allowed_cors: "*"
    idle_in_transaction_session_timeout: "400000"
    graphql_service: "http://integrations/graphql"
    ignore_permissions: "true"
    integration_service: "http://integrations/graphql-admin"
    pool_max: "500"
    pool_acquire: "30000"
    ACCOUNTING_NETSUITE_URL: "http://accountingservice"
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    API_GATEWAY: "http://apigateway"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "http://certificates"
    DB_CONNECTION_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    HOST: "https://website-marketplace.dev.nivodaapi.net/"
    KEYCLOAK_ADMIN_SERVER_URL: "http://keycloak/auth"
    KEYCLOAK_SERVER_URL: "http://keycloak/auth"
    MARKET_PAY_NIVODA_SERVICE: "http://marketpay"
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_SQL_LOGGING: "false"
    PGSSLMODE: "no-verify"
    REDIS_TLS_URL: "rediss://redis-master.marketplace.svc.cluster.local:6379"
    REDIS_URL: "redis://redis-master.marketplace.svc.cluster.local:6379"
    S3_IMAGE_BUCKET: "nivoda-staging"
    SERVER_HOST: "https://website-marketplace.dev.nivodaapi.net"
    SLACK_OUTGOING: "*****************************************************************************"
    TAYLOR_HART_WEBHOOK_URL: "http://demo7656881.mockable.io/taylor"
    TWILIO_RATE_LIMIT_IDENTIFIER: "end_user_phone_number"
    USE_NETSUITE: "true"
    USE_XERO_DEMO: "true"
    WDC_ENV: "staging"
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: "website-marketplace.dev.nivodaapi.net"
    db_pool: ""
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
  environment: marketplace
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/airwallex
    tag: 1_BR_20241220-1343_3485c37
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/airwallex/health_check
      port: 4006
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: airwallex
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/airwallex/health_check
      port: 4006
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4006
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: marketplace
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: airwallex-marketplace.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
