apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: marketplace
  namespace: argocd
spec:
  description: "Project for marketplace environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: marketplace 
  roles:
    - name: admin
      policies:
        - p, proj:marketplace:admin, applications, *, proj:marketplace, allow
        - p, proj:marketplace:admin, clusters, *, *, allow
        - p, proj:marketplace:admin, repositories, *, *, allow
      groups:
        - admins
