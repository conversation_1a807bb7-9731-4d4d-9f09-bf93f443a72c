apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: fintech
  namespace: argocd
spec:
  description: "Project for fintech environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: fintech 
  roles:
    - name: admin
      policies:
        - p, proj:fintech:admin, applications, *, proj:fintech, allow
        - p, proj:fintech:admin, clusters, *, *, allow
        - p, proj:fintech:admin, repositories, *, *, allow
      groups:
        - admins
