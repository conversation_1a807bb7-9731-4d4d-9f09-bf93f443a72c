apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kong-fintech
  namespace: argocd
spec:
  
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: kong
    - repoURL: 'https://charts.konghq.com' 
      targetRevision: "2.46.0"
      chart: kong
      helm:
        releaseName: kong
        valueFiles:
          - $valuesRepo/fintech/kong/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'fintech'
  project: fintech