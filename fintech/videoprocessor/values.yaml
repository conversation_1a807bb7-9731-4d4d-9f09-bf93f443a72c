application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    IMAGE_BUCKET: "nivoda-staging"
    SQS_QUEUE_URL: "https://sqs.eu-west-2.amazonaws.com/676625059805/nivoda-videos-new.fifo"
    PLATFORM_URL: "http://integrations/graphql-admin"
    MAX_MESSAGES_COUNT: "2"
    AWS_ACCESS_KEY_ID: "********************"
    AWS_SECRET_ACCESS_KEY: "1eZzgx7sULLP/fPPMdXb/oHpTu6Ga/rAbehwtevq"
    AWS_SESSION_TOKEN: "IQoJb3JpZ2luX2VjEOb//////////wEaCWV1LXdlc3QtMSJHMEUCIQDvh4Fqr+3G0LgKi85WqJrtx3uiC3AreoDToJlk79F5+wIgIXscOeGANOEdlICD+7GvCk1Ac64w9MNiR4CmF/kjiosq/gIIPhAAGgw2MzM0NzcyMzQ2NzIiDI/+DXK6M67lDUocYirbAkNJhyjNED6UAkKEluHHYLmhBj5qwR6BRVXz/N/K4wV3q7g0k7XcJE2AIYBo5Z8YD/BedVVjrdRVSKJf7NJPLpkkq41/Rrdku+JI0Sti0eH+ceCsmQxtliuecZOKFPaCsj7NzHJ0u607lkTy2levZdNAk5XObgpGSrq1/9Nq60BVuW4G63HLjNdxx7bLm4W+AhlAxWAPaV4vnbUhzhhAgIKpfInNbMI9SymspqoysnEUdIZfmI6o/zkFtz4dkEIA+zGRK+BZEYTnAhHRVy4/FMLMlgunwsAdBWESsH/2/yst09EYhmjooSS1xlfi2BdrQAPa3j0J/UYjBa/BvMLFyd+j8IaVb5uVvLzmHy9enB93Z7id47SNnXA9OiRePG9xU8UaQ4VsfOKkZtLtG0wWwXiaqQqIdB/A86DaI0pce/pu007vso66dcsZQ6F28VQDAwBzN2t0ahdyCprWMLb+krgGOqcBCXKZhJMQo7bH4eDSyKw6d10nWUnnfoLtBWVtzpmQ9ChKhu45RJVGlwtwzf6MYw9j2i1cSEKFPkvNam2gA3dVxT0pJviQ6tXQgD9mFOolE6P4FhcS75+b08uITU2Pk7KcIp934yjYYr/fwGnIMJwNx7YH0wP5qqW49wHHR5DtKgkAt8b3Jn5Lya92XLpZP4eC6jO5Dj3Q7V94kEEQ2YdLDQKvF3N+svs="
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_CONSUMER_TOPIC: "NivodaEvents"
    USE_KAFKA: "true"
    KAFKA_GROUP_ID: "video-processor"
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
    RUN_FFMPEG_INSTALLER: "false"
    HOST: "0.0.0.0"
    MP4_PROCESSING_ENABLED: "true"
  environment: fintech
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/videoprocessor
    tag: 1_BR_20241216-1451_7540a87
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 5000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: videoprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 5000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 5000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: fintech
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: videoprocessor-fintech.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
