application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    ADMIN_PASS: Nivoda123
    ADMIN_USER: <EMAIL>
    AIRWALLEX_ACCOUNT_ID_AU: acct_8cD5-vwMN0qKsQVs2xuCKw
    AIRWALLEX_ACCOUNT_ID_BE: acct_8cD5-vwMN0qKsQVs2xuCKw
    AIRWALLEX_ACCOUNT_ID_NL: acct_8cD5-vwMN0qKsQVs2xuCKw
    AIRWALLEX_ACCOUNT_ID_UK: acct_8cD5-vwMN0qKsQVs2xuCKw
    AIRWALLEX_ACCOUNT_ID_US: acct_8cD5-vwMN0qKsQVs2xuCKw
    AIRWALLEX_API_KEY: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_API_KEY_AU: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_API_KEY_BE: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_API_KEY_HK: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_API_KEY_NL: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_API_KEY_US: 45c8f762e7769d603d8a84bbb48230cb643afad8128a9e6e128279c7bdece264972e8a96d55b61b411199b55a38945b7
    AIRWALLEX_BASE_URL: https://api-demo.airwallex.com/api/v1
    AIRWALLEX_CLIENT_ID: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_CLIENT_ID_AU: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_CLIENT_ID_BE: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_CLIENT_ID_HK: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_CLIENT_ID_NL: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_CLIENT_ID_US: nmosMsRuQ5-xQ0PpnL4TSw
    AIRWALLEX_WEBHOOK_SECRET: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_AU: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_BE: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_HK: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_NL: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_UK: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    AIRWALLEX_WEBHOOK_SECRET_US: whsec_p9D3efYUq74PCfwR9Ts1DCqeJX2fgALZ
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_ENABLED: 'true'
    CERTIFICATE_HOST: http://certificates
    CREDIT_CHARGE: '2.5'
    DB_CONNECTION_READ_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    DB_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    DEAD_LETTER_QUEUE: PaymentsDLQ
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    HOST: https://website-fintech.dev.nivodaapi.net/
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: airwallex-app-fintech-deposit-consumer
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: PaymentEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_SQL_LOGGING: 'false'
    NODE_ENV: production
    PGSSLMODE: no-verify
    QUEUE_ENABLED: 'false'
    REDIS_TLS_URL: rediss://redis-master.fintech.svc.cluster.local:6379
    REDIS_URI_BULL: redis-master.fintech.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.fintech.svc.cluster.local:6379
    S3_IMAGE_BUCKET: nivoda-staging
    SERVER_HOST: https://website-fintech.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_NETSUITE: 'true'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-fintech.dev.nivodaapi.net
    allowed_cors: '*'
    db_pool: ''
    graphql_service: https://integrations-fintech.dev.nivodaapi.net/graphql
    idle_in_transaction_session_timeout: '400000'
    ignore_permissions: 'true'
    integration_service: https://integrations-fintech.dev.nivodaapi.net/graphql-admin
    pool_acquire: '30000'
    pool_max: '500'
    port: '4006'
  environment: fintech
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/airwallex
    tag: 1_BR_20250723-1828_4e760e3
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/airwallex/health_check
      port: 4006
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: airwallex
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/airwallex/health_check
      port: 4006
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4006
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: public-ingress
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: airwallex-fintech.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
