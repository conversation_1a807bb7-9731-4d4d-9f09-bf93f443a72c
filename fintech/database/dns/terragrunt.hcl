terraform {
  source = "*****************:nivoda/infrastructure-modules.git//route53?ref=staging"
  # source = "/Users/<USER>/Documents/projects/infrastructure-modules/route53"
}

include "root" {
  path = find_in_parent_folders("root.hcl")
  }

include "env" {
  path           = find_in_parent_folders("env.hcl")
  expose         = true
  merge_strategy = "no_merge"
}

inputs = {

  env = include.env.locals.env
  hosted_zone_id = "Z01197843N9DUJ8C3OCMC"
  metadata = [{
    ingress_name    = "db-writer"
    entry_type      = "CNAME"
    records_value   = [dependency.db.outputs.rds_hostname]
  }]

}

dependency "db" {
  config_path = "../rds"

  mock_outputs = {
    endpoint = ["10.0.0.0"]
  }
}