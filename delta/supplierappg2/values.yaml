application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    NX_ADD_PLUGINS: "false"
    NX_CLOUD_ACCESS_TOKEN: "NTlmZmI0NWMtOTQ2Ni00ZWVjLWI4YWQtYzljY2RmNzdhY2QyfHJlYWQtd3JpdGU="

    # App Core
    NODE_ENV: "production"
    VITE_APP_GOOGLE_RECAPTCHA_SITE_KEY: "6LeXUW0UAAAAAPp9HgNKDu1I6xCbqAtCjyba2soX"
    NPM_TOKEN: "************************************"
    VITE_APP_NAME: "supplier"

    # App Endpoints/URLs
    VITE_APP_GATEWAY_REDIRECT: "https://fegateway-delta.nivodaapi.net"
    VITE_APP_SUPPLIER_REDIRECT: "https://supplierappg2-delta.nivodaapi.net"
    # VITE_APP_STATIC_URI: http://localhost:4206

    # Graphql Eendpoints
    VITE_APP_BASE_GRAPHQL_URI: "https://integrations-delta.nivodaapi.net"
    VITE_TMS_BASE_GRAPHQL_URI: "https://translation-delta.nivodaapi.net"

    # Datadog RUM
    VITE_RUM_ENV: "development"
    VITE_RUM_SERVICE: "nivoda-platform"
    VITE_SAMPLE_RATE: "100"
    VITE_REPLAY_RATE: "1"

    # App Live Chat
    VITE_APP_CS_CHAT_WIDGET_TOKEN: "21709827"
    VITE_APP_CHAT_ENABLED: "true"

    # Prod Env vars
    #VITE_RUM_APP_ID: 
    #VITE_RUM_CLIENT_TOKEN: 
    #VITE_RUM_APP_ACTIVE: 
  environment: delta
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/supplierappg2
    tag: 1_BR_20241218-1718_97cb3e72_coreui
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: supplierappg2
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 80
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: delta
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: supplierappg2-delta.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
