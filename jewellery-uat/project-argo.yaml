apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: jewellery-uat
  namespace: argocd
spec:
  description: "Project for jewellery-uat environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: jewellery-uat 
  roles:
    - name: admin
      policies:
        - p, proj:jewellery-uat:admin, applications, *, proj:jewellery-uat, allow
        - p, proj:jewellery-uat:admin, clusters, *, *, allow
        - p, proj:jewellery-uat:admin, repositories, *, *, allow
      groups:
        - admins
