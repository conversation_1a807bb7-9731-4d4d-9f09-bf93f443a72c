application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node observers/Consumer.js
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_NIVODA_SERVICE: http://airwallex
    ALLIANZ_NIVODA_SERVICE: http://allianz
    ALLIANZ_POLICY_ID: '*********'
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_API_KEY: test
    APOLLO_ENGINE_ENABLED: 'true'
    AUTH_URL: https://website2-jewellery.dev.nivodaapi.net
    CERTIFICATE_HOST: http://certificates
    CERT_REMOVAL_EVENTS: 'true'
    CONVERT_API_SECRET: lKXs7jqHKWg54qmM
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    DATABASE_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    DB_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    DIAMONDS_SERVICE_STAGING: http://diamonds
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    ENGAGESPOT_API_KEY: 8veygg97djlrgqm2z0rx3s
    ENGAGESPOT_API_SECRET: r41h73o0blkrgqee5f3kl564gai86g4fa7356a2eajhf3iie
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    GEMSTONES_SERVICE_STAGING: http://gemstonesprocessor
    HOST: https://website2-jewellery.dev.nivodaapi.net
    JWT_SECRET: secretjaleukoke
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: jewellery-uat
    KAFKA_HIGH_PRIORITY_EVENT: HighPriorityEvents
    KAFKA_MAIN_EVENT: JewelryEventsUAT
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: JewelryEventsUAT
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: production
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_SERVICE_NAME: integrations-jewellery-uat
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    POSTMEN_KEY: 7d3e527f-cfbd-4e07-99db-88d10053f08e
    POSTMEN_TEST_KEY: c9bc93a5-7934-481b-ab1c-37b262363892
    REACT_APP_GOOGLE_RECAPTCHA: 6LcKmywmAAAAABBX0bnfL0IeznCOazdLQz9xBULA
    REDIS_TLS_URL: rediss://redis-master.jewellery-uat.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.jewellery-uat.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.jewellery-uat.svc.cluster.local:6379
    REDSHIFT_DATABASE: bi_server
    REDSHIFT_HOST: ************
    REDSHIFT_PASSWORD: ahmed@1KNuB
    REDSHIFT_PORT: '5439'
    REDSHIFT_USER: ahmed
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-jewellery.dev.nivodaapi.net
    SHARING_HOST: https://sharing-cx.dev.nivodaapi.net/diamonds?share_list_id=
    SLACK_OUTGOING: *****************************************************************************
    SONARQUBE_SERVER_URL: http://*************:9000
    SONARQUBE_TOKEN: ****************************************
    SQS_QUEUE_URL: https://sqs.us-east-2.amazonaws.com/242555748887/nivoda-demo.fifo
    STOCK_REMOVAL_EVENTS: 'true'
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_SID: AC7a2f726ccded8b8a0e151b6c4700791d
    TWILIO_TOKEN: melee-twilio-token
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-jewellery.dev.nivodaapi.net
    ZENDESK_API_KEY: 3425cd5751442adee7c5c25ceb00330680692d06f34b977562d37fe47d555893
    ZENDESK_API_TOKEN: HxrRwPYG7wL0TO1g9iTUulI9NswtTvZN8ijJU4Vq
    ZENDESK_API_URL: https://nivodahelp1732194167.zendesk.com/api/v2
    ZENDESK_BASE_URL: https://nivodahelp.zendesk.com/
    ZENDESK_COUNTRY_FIELD_ID: '30263185294481'
    ZENDESK_JWT_API_KEY: app_674d433e337dd8ccfa323cce
    ZENDESK_JWT_SECRET: GaKOgqXMCejR-4aBcP9tK37N8nWKMVAdGFaofa-Rn1ZgG7KBaf3fSENCvf_a3aonZqClfOR4cJlNqyUrNy7uoA
    ZENDESK_USERNAME: <EMAIL>
    environment: jewellery
    idle_in_transaction_session_timeout: '400000'
    okta_client_id: 0oakqh8k926XUNwHR5d7
    okta_issuer: https://dev-32165027.okta.com/oauth2/default
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250605-0926_e1434e58f0
  imagePullSecrets: []
  labels: {}
  name: jewellery-sku-kafka-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
