apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kong-jewellery-uat
  namespace: argocd
spec:
  
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: kong
    - repoURL: 'https://charts.konghq.com' 
      targetRevision: "2.46.0"
      chart: kong
      helm:
        releaseName: kong
        valueFiles:
          - $valuesRepo/jewellery-uat/kong/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'jewellery-uat'
  project: jewellery-uat