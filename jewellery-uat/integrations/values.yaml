application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 3
    minReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  args:
    - --require
    - ./instrumentation.js
    - app.js
  nodeSelector:
    eks.amazonaws.com/nodegroup: redis
  tolerations:
    - key: "where"
      operator: "Equal"
      value: "redis"
      effect: "NoSchedule"
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_ENABLED: 'true'
    CERTIFICATE_HOST: http://certificates
    DATABASE_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    DB_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/jewellery-uat
    HOST: https://website-jewellery-uat.dev.nivodaapi.net/
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: staging
    OTEL_EXPORTER_OTLP_ENDPOINT: http://signoz-otel-collector.signoz:4318/v1/traces
    OTEL_SERVICE_NAME: integrations-jewellery-uat
    OTEL_RESOURCE_ATTRIBUTES: service.name=integrations-jewellery-uat,service.version=1.0.0,deployment.environment=jewellery-uat
    OTEL_TRACES_EXPORTER: otlp
    OTEL_SERVICE_VERSION: 1.0.0
    PGSSLMODE: no-verify
    PORT: '3000'
    REDIS_TLS_URL: rediss://redis-master.jewellery-uat.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.jewellery-uat.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.jewellery-uat.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-jewellery-uat.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: https://website-jewellery-uat.dev.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: jewellery-uat
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250605-0926_e1434e58f0
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: integrations
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 2
  resources:
    limits:
      cpu: 700m
      ephemeral-storage: 2Gi
      memory: 3000Mi
    requests:
      cpu: 700m
      ephemeral-storage: 1Gi
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: jewellery-uat
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: integrations-jewellery-uat.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: true
