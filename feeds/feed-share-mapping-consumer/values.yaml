application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    AUTO_OFFSET_RESET: earliest
    DBCONNECTIONINTEGRATIONSURL: postgresql://integrations:<EMAIL>/automation
    ENABLE_AUTO_COMMIT: 'false'
    ENVIRONMENT: STAGING
    HOST: 0.0.0.0
    IMAGE_BUCKET: internal-test-2023
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: KYCEvents
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: feedconsumer
    <PERSON>AFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    LOG_LEVEL: error
    MAX_MESSAGES_COUNT: '2'
    MAX_POLL_INTERVAL_MS: '300000'
    MAX_POLL_RECORDS: '500'
    PLATFORM_URL: http://integrations/graphql-admin
    RUN_FFMPEG_INSTALLER: 'false'
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/676625059805/nivoda-videos-new.fifo
    USE_KAFKA: 'true'
  environment: feeds
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/feed-share-mapping-consumer
    tag: 1_BR_20250425-1021_a15927b
  imagePullSecrets: []
  labels: {}
  name: feed-share-mapping-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
