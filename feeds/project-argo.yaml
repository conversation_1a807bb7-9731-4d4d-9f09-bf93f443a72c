apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: feeds
  namespace: argocd
spec:
  description: "Project for feeds environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: feeds 
  roles:
    - name: admin
      policies:
        - p, proj:feeds:admin, applications, *, proj:feeds, allow
        - p, proj:feeds:admin, clusters, *, *, allow
        - p, proj:feeds:admin, repositories, *, *, allow
      groups:
        - admins
