application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 3
    minReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    AWS_ACCESS_KEY_ID: ********************
    AWS_SECRET_ACCESS_KEY: EY9bkrztxLMJP7xLSlT0c5ZUyC/HNFS3Irr1yivP
    DBCONNECTIONINTEGRATIONSURL: postgresql://postgres:<EMAIL>:5432/automation
    DBCONNECTIONREADURL: postgresql://postgres:<EMAIL>/feeds-db-csv
    DBCONNECTIONURL: postgresql://postgres:<EMAIL>/feeds-db-csv
    ENVIRONMENT: STAGING
    HOST: 0.0.0.0
    IMAGE_BUCKET: internal-test-2023
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: feedspushfeedsenv
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: feedsconsumerfeedsenv
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    MAX_MESSAGES_COUNT: '2'
    PLATFORM_URL: https://integrations-feeds.dev.nivodaapi.net/graphql-admin
    RUN_FFMPEG_INSTALLER: 'false'
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/************/nivoda-videos-new.fifo
    USE_KAFKA: 'true'
  environment: feeds
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/feedsconsumer
    tag: 1_BR_20250611-1308_45c021e
  imagePullSecrets: []
  labels: {}
  name: feedsconsumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 3
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
