terraform {
  source = "*****************:nivoda/infrastructure-modules.git//route53?ref=v0.0.7"
  # source = "/Users/<USER>/Documents/projects/infrastructure-modules/route53"
}

include "root" {
  path = find_in_parent_folders("root.hcl")
}

include "env" {
  path           = find_in_parent_folders("env.hcl")
  expose         = true
  merge_strategy = "no_merge"
}

inputs = {

  env = include.env.locals.env
  hosted_zone_id = "Z01197843N9DUJ8C3OCMC"
  metadata = [{
    ingress_name    = "database"
    entry_type      = "A"
    records_value   = dependency.db.outputs.instance_private_ips
  }]

}

dependency "db" {
  config_path = "../ec2"

  mock_outputs = {
    instance_private_ips = ["10.0.0.0"]
  }
}