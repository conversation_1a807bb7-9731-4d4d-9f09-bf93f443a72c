apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: express-apps
  namespace: argocd
spec:
  source:
    path: ./express/apps
    repoURL: 'https://bitbucket.org/nivoda/gitops.git'
    targetRevision: dev
  destination:
    namespace: argocd
    server: 'https://kubernetes.default.svc'
  project: default
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    # retry:
    #   limit: 5
    #   backoff:
    #     duration: 5s
    #     maxDuration: 3m0s
    #     factor: 2
    syncOptions:
      - CreateNamespace=true