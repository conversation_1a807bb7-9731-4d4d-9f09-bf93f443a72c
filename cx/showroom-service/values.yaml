application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 3
    minReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: https://accounting-service-3197a576c6503f9f.onporter.run
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    APOLLO_ENGINE_ENABLED: 'true'
    AUTH_URL: https://fegateway-cx.dev.nivodaapi.net
    CERTIFICATE_HOST: https://certificates-qa.nivodaapi.net
    DATABASE_URL: postgresql://integrations:<EMAIL>/cx
    DB_URL: postgresql://integrations:<EMAIL>/cx
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/cx
    HOST: https://website-cx.dev.nivodaapi.net
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-cx.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-cx.nivodaapi.net/auth
    LOUPE360_SPLASH_BUCKET: dev-nivoda-staging
    MARKET_PAY_NIVODA_SERVICE: https://porter-marketpay-dev.nivodaapi.net
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: production
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_SERVICE_NAME: showroom-service-cx
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    REDIS_TLS_URL: rediss://redis-master.cx.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.cx.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.cx.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SEARCH_SERVICE_ENABLED: 'true'
    SEARCH_SERVICE_TEXT_SEARCH_DISABLED: 'false'
    SEARCH_SERVICE_URL: http://searchservice.cx.svc.cluster.local
    SERVER_HOST: https://website-cx.dev.nivodaapi.net
    SERVER_SEGMENT_WRITEKEY: KPF6w3kh2wBhLwyORn11Ysjc1wYpe1lt
    SKIP_FEATURE_FLAG_SEARCH: enabled
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'true'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-cx.dev.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: cx
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/showroom-service 
    tag: 1_BR_20250709-1250_da962c7002
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: showroom-service
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 2
  resources:
    limits:
      cpu: 300m
      memory: 3000Mi
      ephemeral-storage: 2Gi
    requests:
      cpu: 300m
      memory: 3000Mi
      ephemeral-storage: 1Gi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
- name: showroom-service
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: cx
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: showroom-service-cx.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: false 