apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: cx
  namespace: argocd
spec:
  description: "Project for cx environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: cx 
  roles:
    - name: admin
      policies:
        - p, proj:cx:admin, applications, *, proj:cx, allow
        - p, proj:cx:admin, clusters, *, *, allow
        - p, proj:cx:admin, repositories, *, *, allow
      groups:
        - admins
