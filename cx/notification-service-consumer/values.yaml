application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node devops/kafka/Consumer.js
  env:
    DB_DATABASE: cx
    DB_HOST: db-writer-bx.dev.nivodaapi.net
    DB_INTEGRATIONS_DATABASE: cx
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_PORT: '5432'
    DB_USERNAME: integrations
    INTEGRATIONS_BASE_URL: http://localhost:3000
    INTEGRATIONS_PASSWORD: '123456'
    INTEGRATIONS_USERNAME: testname
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_ACCESS_KEY: ********************
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: notificationservice
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SECRET_ACCESS_KEY: /Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7
    KAFKA_SSL: enabled
    KAFKA_TOPIC: NotificationEvents
    KAFKA_USERNAME: nivoda
    LOG_LEVEL: db
    NODE_ENV: staging
    PORT: '3005'
    WDC_ENV: staging
  environment: cx
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/notifications
    tag: 1_BR_20250724-0813_6b6261b
  imagePullSecrets: []
  labels: {}
  name: notification-service-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
