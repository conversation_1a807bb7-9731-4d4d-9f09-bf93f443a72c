application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DB_DATABASE: cx
    DB_HOST: db-writer-bx.dev.nivodaapi.net
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_PORT: '5432'
    DB_USERNAME: integrations
    EXPO_ACCESS_TOKEN: '123456'
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: notificationservice
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: NotificationEvents
    LOG_LEVEL: db
    NODE_ENV: development
    PORT: '3005'
    SES_ACCESS_KEY_ID: '123456'
    SES_SECRET_ACCESS_KEY: '123456'
    TWILIO_ACCOUNT_SID: AC54321
    TWILIO_AUTH_TOKEN: '123456'
    WINSTON_TO_FILE: 'true'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: cx
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/notifications
    tag: 1_BR_20250724-0813_6b6261b
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: notificationservice
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 300m
      ephemeral-storage: 2Gi
      memory: 3000Mi
    requests:
      cpu: 300m
      ephemeral-storage: 1Gi
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3005
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: cx
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: notificationservice-cx.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: false
