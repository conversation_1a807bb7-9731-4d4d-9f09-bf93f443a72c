monitoringPasscodeSecretName: sonarqube-monitoring-pass
monitoringPasscodeSecretKey: monitoring-passcode
edition: developer
image:
  repository: sonarqube
  tag: lts
  pullPolicy: IfNotPresent
  pullSecrets:
    - name: docker-secret

persistence:
  enabled: true
  storageClass: gp2 # or your preferred StorageClass
  size: 10Gi

service:
  type: ClusterIP
  port: 9000

ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:633477234672:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    # alb.ingress.kubernetes.io/healthcheck-path: /api/system/health
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  hosts:
    - name: sonarqube-core-be.dev.nivodaapi.net
      path: /
      pathType: Prefix
  pathType: Prefix

postgresql:
  enabled: true
  postgresqlUsername: sonar
  postgresqlPassword: sonarpass
  postgresqlDatabase: sonarqube

resources:
  limits:
    cpu: 1000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

securityContext:
  fsGroup: 1000
  runAsUser: 1000
