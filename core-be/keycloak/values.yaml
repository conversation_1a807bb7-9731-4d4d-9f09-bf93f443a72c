application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    AUTHENTICATOR: "KEYCLOAK"
    DB_ADDR: "primary-dev-cluster.cluster-cvvzhnezgjyi.eu-west-2.rds.amazonaws.com"
    DB_DATABASE: "keycloak-gama"
    DB_PASSWORD: "rQrlEC17pboABB4vKNuB"
    DB_SCHEMA: "public"
    DB_USER: "postgres"
    DB_VENDOR: "POSTGRES"
    KC_HTTP_HOST: "0.0.0.0"
    KC_PROXY: "passthrough"
    KEYCLOAK_ADMIN_SERVER_URL: "https://keycloak-core-be.dev.nivodaapi.net/auth"
    KEYCLOAK_CLIENTID: "nivodaapp"
    KEYCLOAK_EXTRA_ARGS: "JAVA_OPTS_APPEND=--Djgroups.dns.query=headless-service.dev.svc.cluster.local"
    KEYCLOAK_FRONTEND_URL: "https://keycloak-core-be.dev.nivodaapi.net/auth/"
    KEYCLOAK_PASSWORD: "Pa55w0rd"
    KEYCLOAK_REALM: "Nivoda"
    KEYCLOAK_SECRET: "vvdGsyCIZh3PioqIdwVm0Glco51zaMPH"
    KEYCLOAK_SERVER_URL: "https://keycloak-core-be.dev.nivodaapi.net/auth"
    KEYCLOAK_USER: "admin"
  environment: core-be
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/keycloak
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /auth
      port: 8080
    initialDelaySeconds: 300
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: keycloak
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /auth
      port: 8080
    initialDelaySeconds: 300
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 200m
      memory: 1200Mi
    requests:
      cpu: 200m
      memory: 1200Mi
  securityContext: {}
  service:
    port: 8080
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: keycloak-core-be.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
