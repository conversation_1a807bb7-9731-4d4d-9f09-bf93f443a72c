apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: airflow-core-be
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: airflow
    - repoURL: "https://airflow.apache.org"
      targetRevision: "1.15.0"
      chart: airflow
      helm:
        releaseName: airflow
        valueFiles:
          - $valuesRepo/core-be/airflow/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'core-be'
  project: core-be 