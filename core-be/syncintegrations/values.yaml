application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    PORT: "3005"
    NODE_ENV: "production"
    DEBUG: "development"
    DB_HOST: "primary-dev-db.nivodaapi.net"
    DB_DATABASE: "sync-integrations"
    DB_PORT: "5432"
    DB_USERNAME: "integrations"
    DB_PASSWORD: "rQrlEC17pboABB4vKNuB"
    DB_INTEGRATIONS_HOST: "db-writer-fintech.dev.nivodaapi.net"
    DB_INTEGRATIONS_DATABASE: "credits"
    DB_INTEGRATIONS_PORT: "5432"
    DB_INTEGRATIONS_USERNAME: "integrations"
    DB_INTEGRATIONS_PASSWORD: "rQrlEC17pboABB4vKNuB"
    INTEGRATIONS_BASE_URL: "https://api.fintech.dev.nivodaapi.net"
    INTEGRATIONS_USERNAME: "3p"
    INTEGRATIONS_PASSWORD: "dOnOtmisuse@11"
    HUBSPOT_BASE_URL: "https://api.hubapi.com"
    HUBSPOT_API_KEY: "pat-na1-************************************"
    FIRST_STAGE_ID_CUSTOMER: "726ed90f-dde6-4936-9a9c-21c1d6a15448"
    FIRST_STAGE_ID_SUPPLIER: 34660662
    PIPELINE_ID_CUSTOMER: "15403bf0-4bff-402a-a3a4-f284f1f4869d"
    PIPELINE_ID_SUPPLIER: 11712280
    WINSTON_TO_FILE: "true"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_ENABLED: "true"
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_GROUP_ID: "sync-integrations"
    KAFKA_TOPIC: "sync-entity"
    ZENDESK_API_KEY: bd2d9bf388c6b4b28b2e68242f045d01bc53a5a7f269cc92cb390302759afd38
    ZENDESK_BASE_URL: https://nivodahelp1732194167.zendesk.com
    ZENDESK_COUNTRY_FIELD_ID="30263185294481"
  environment: core-be
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/syncintegrations
    tag: 1_BR_20250210-1450_d0b942d
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: syncintegrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3005
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: syncintegrations-core-be.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
