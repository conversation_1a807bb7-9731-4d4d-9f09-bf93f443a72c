application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: npm run start-consumer
  env:
    DATABASE_URL: postgresql://integrations:<EMAIL>/b2c-plugin-shopify
    EXCLUDE_SUPPLIER_IDS: 4c2b578b-da7c-4627-a6d0-d03ddf406aaa,e961d9bf-d682-401e-a789-2614a95805dc,89274cb4-c11a-4d43-b01b-3ec74de0c6c4
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: shopify-app
    KAFKA_MAIN_EVENT: SD-stock-price-updates
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    MONETIZATION_ACTIVE: 'true'
    NODE_ENV: development
    PORT: '8081'
    SCOPES: read_content,read_metaobject_definitions,read_metaobjects,read_orders,read_publications,read_script_tags,read_themes,unauthenticated_read_product_listings,write_content,write_files,write_metaobject_definitions,write_metaobjects,write_products,write_publications,write_script_tags,write_themes,write_fulfillments,read_locations,write_locations,read_assigned_fulfillment_orders,write_assigned_fulfillment_orders,write_third_party_fulfillment_orders
    SENTRY_NODE_ENV: staging
    SHOPIFY_API_KEY: d8abf38d35813c5a7051df1ac3a92d0c
    SHOPIFY_API_SECRET: 6b634107413e3487206ab1c69e73e5e9
    SHOPIFY_APP_URL: https://b2c-plugin-shopify-core-be.dev.nivodaapi.net/
    SHOPIFY_FEEDS_API: http://integrations/graphql-loupe360
    SHOPIFY_ORDERS_API: http://integrations/graphql-shopify
  environment: core-be
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/b2c-plugin-shopify
    tag: 1_BR_20250722-0529_7022018
  imagePullSecrets: []
  labels: {}
  name: shopify-b3c-plugin-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
