application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    CORS_ORIGIN: https://website-coreui.dev.nivodaapi.net,https://website-fintech.dev.nivodaapi.net,https://website-gems.dev.nivodaapi.net,https://website-testautomation.dev.nivodaapi.net,https://website-core-be.dev.nivodaapi.net,https://website-melee.dev.nivodaapi.net,https://website-automation-cdn.dev.nivodaapi.net,https://website-credits.dev.nivodaapi.net,https://website-alpha.dev.nivodaapi.net,https://website-supplier.dev.nivodaapi.net,https://website-cfm.dev.nivodaapi.net,https://website-internalops.dev.nivodaapi.net,https://website-beta.dev.nivodaapi.net,http://localhost:3001,https://website-cx.dev.nivodaapi.net,https://website-lfg.dev.nivodaapi.net,https://website2-jewellery.dev.nivodaapi.net,https://website-jewellery.dev.nivodaapi.net
    DATABASE_URL: postgresql://integrations:<EMAIL>/translation-finops
    NODE_ENV: staging
    PORT: '4000'
    REDIS_URL: redis://redis-master.core-be.svc.cluster.local:6379
    USE_S3: 'false'
  environment: core-be
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/translations
    tag: 1_BR_20250723-1746_3d5cf79
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /translation-health
      port: 4000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: translations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /translation-health
      port: 4000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: translations-core-be.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
