apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: keycloak23-manual
  namespace: core-be
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:633477234672:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
spec:
  rules:
    - host: keycloak23-core-be.dev.nivodaapi.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keycloak23
                port:
                  number: 80
