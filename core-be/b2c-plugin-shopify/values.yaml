application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DATABASE_URL: postgresql://integrations:<EMAIL>/b2c-plugin-shopify
    EXCLUDE_SUPPLIER_IDS: 4c2b578b-da7c-4627-a6d0-d03ddf406aaa,e961d9bf-d682-401e-a789-2614a95805dc,89274cb4-c11a-4d43-b01b-3ec74de0c6c4
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: shopify-app
    KAFKA_MAIN_EVENT: NivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    MONETIZATION_ACTIVE: 'true'
    PORT: '8081'
    SCOPES: read_content,read_metaobject_definitions,read_metaobjects,read_orders,read_publications,read_script_tags,read_themes,unauthenticated_read_product_listings,write_content,write_files,write_metaobject_definitions,write_metaobjects,write_products,write_publications,write_script_tags,write_themes,write_fulfillments,read_locations,write_locations,read_assigned_fulfillment_orders,write_assigned_fulfillment_orders,write_third_party_fulfillment_orders,read_inventory,write_inventory
    SENTRY_NODE_ENV: staging
    SHOPIFY_API_KEY: d8abf38d35813c5a7051df1ac3a92d0c
    SHOPIFY_API_SECRET: 6b634107413e3487206ab1c69e73e5e9
    SHOPIFY_APP_URL: https://b2c-plugin-shopify-core-be.dev.nivodaapi.net/
    SHOPIFY_FEEDS_API: http://integrations/graphql-loupe360
    SHOPIFY_ORDERS_API: http://integrations/graphql-shopify
  environment: core-be
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/b2c-plugin-shopify
    tag: 1_BR_20250722-0529_7022018
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/health
      port: 8081
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: b2c-plugin-shopify
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/health
      port: 8081
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 8081
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: core-be
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: b2c-plugin-shopify-core-be.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
