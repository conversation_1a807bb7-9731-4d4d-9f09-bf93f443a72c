apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: core-be
  namespace: argocd
spec:
  description: "Project for core-be environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: core-be 
  roles:
    - name: admin
      policies:
        - p, proj:core-be:admin, applications, *, proj:core-be, allow
        - p, proj:core-be:admin, clusters, *, *, allow
        - p, proj:core-be:admin, repositories, *, *, allow
      groups:
        - admins
