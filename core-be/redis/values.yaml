auth:
  enabled: false
cluster:
  enabled: false
global:
  storageClass: gp2
  defaultStorageClass: gp2
master:
  persistence:
    enabled: false
replica:
  persistence:
    enabled: false
architecture: standalone
image:
  pullSecrets:
    - name: docker-secret
#   registry: 633477234672.dkr.ecr.eu-west-2.amazonaws.com
#   repository: redis
#   tag: latest
master:
  # resources:
  #   requests:
  #     memory: 256Mi
  #     cpu: 100m
  nodeSelector: 
    eks.amazonaws.com/nodegroup: redis
  tolerations:
    - key: "where"
      operator: "Equal"
      value: "redis"
      effect: "NoSchedule"