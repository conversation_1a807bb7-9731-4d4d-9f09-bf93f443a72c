apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fluentbit
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
       selfHeal: true
    syncOptions:
      - CreateNamespace=true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: fluentbit
    - repoURL: "https://fluent.github.io/helm-charts"
      targetRevision: "0.48.3"
      chart: fluent-bit
      helm:
        releaseName: fluentbit
        valueFiles:
          - $valuesRepo/fluentbit/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'fluentbit'
  project: fluentbit

  # helm upgrade --install fluentbit fluent/fluent-bit \
  # --namespace fluentbit \
  # --create-namespace \
  # --version 0.48.3 \
  # -f values.yaml
