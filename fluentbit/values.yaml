backend:
  type: kafka
  kafka:
    brokers: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    topics: "containerlogs"
    sasl:
      enabled: true
      mechanism: SCRAM-SHA-512
      username: nivoda
      password: nivoda123
    tls:
      enabled: true
      verify: true
    format: json
image:
  repository: 633477234672.dkr.ecr.eu-west-2.amazonaws.com/monitoring/fluentbit
  tag: linux
config:
  inputs: |
    [INPUT]
        Name    tail
        Tag     kube.core.*
        Path    /var/log/containers/*_core_b2c-*.log
        multiline.parser docker, cri
  filters: |
    [FILTER]
        name grep
        Match kube.core.*
        regex  log .*event.*(api_feed|api_feed_id|shopify_webhook_received).*

  parsers: |
    [PARSER]
        Name   json
        Format json
        Time_Key timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%LZ
 
  outputs: |
    [OUTPUT]
        Name    kafka
        Match   kube.core.*
        Brokers b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
        Topics  containerlogs
        rdkafka.sasl.mechanisms SCRAM-SHA-512
        rdkafka.sasl.username nivoda
        rdkafka.sasl.password nivoda123
        rdkafka.security.protocol SASL_SSL
        rdkafka.ssl.verify false                
       