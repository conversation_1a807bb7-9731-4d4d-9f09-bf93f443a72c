apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: fluentbit
  namespace: argocd
spec:
  description: "Project for fluentbit environment"
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: fluentbit
    - server: 'https://kubernetes.default.svc'
      namespace: '*'
  roles:
    - name: admin
      policies:
        - p, proj:fluentbit:admin, applications, *, proj:fluentbit, allow
        - p, proj:fluentbit:admin, clusters, *, *, allow
        - p, proj:fluentbit:admin, repositories, *, *, allow
      groups:
        - admins
