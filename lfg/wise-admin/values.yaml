application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    DB_HOST: dev-cluster.cluster-cvvzhnezgjyi.eu-west-2.rds.amazonaws.com
    DB_NAME: default
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_USER: integrations
    NODE_ENV: production
    NPM_TOKEN: ************************************
    NX_ADD_PLUGINS: 'false'
    VITE_APP_ADMIN_REDIRECT: https://website-lfg.nivodaapi.net
    VITE_APP_BASE_GRAPHQL_URI: https://integrations-lfg.nivodaapi.net
    VITE_APP_CHAT_ENABLED: 'true'
    VITE_APP_CS_CHAT_WIDGET_TOKEN: '21709827'
    VITE_APP_CUSTOMER_REDIRECT: https://website-lfg.nivodaapi.net
    VITE_APP_GATEWAY_REDIRECT: https://fegateway-lfg.nivodaapi.net
    VITE_APP_GOOGLE_RECAPTCHA_SITE_KEY: 6LfTcEAmAAAAAFd7Q8hh9KuJklg0GPHiLVL0JZxt
    VITE_APP_LEGACY_WEBSITE_REDIRECT: 'true'
    VITE_APP_NAME: gateway
    VITE_APP_SUPPLIER_REDIRECT: https://website-lfg.nivodaapi.net
    VITE_REPLAY_RATE: '1'
    VITE_RUM_ENV: development
    VITE_RUM_SERVICE: nivoda-platform
    VITE_SAMPLE_RATE: '100'
    VITE_TMS_BASE_GRAPHQL_URI: https://apigateway-lfg.nivodaapi.net
  environment: lfg
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/wise-admin
    tag: 1_BR_20250729-1602_144b64115_lfg
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: wise-admin
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 80
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 200m
      memory: 600Mi
    requests:
      cpu: 200m
      memory: 600Mi
  securityContext: {}
  service:
    port: 80
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: lfg
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: wise-admin-lfg.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
