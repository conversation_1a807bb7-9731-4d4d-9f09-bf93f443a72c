application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    enabled: false
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/observers/Consumer.js
  env:
    DB_HOST: db-writer-lfg.dev.nivodaapi.net
    DB_NAME: wisecertificates
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_PORT: '5432'
    DB_USERNAME: integrations
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: wise-consumer-lfg
    KAFKA_MAIN_EVENT: WiseEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    PORT: '4001'
  environment: lfg
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/wise-certificates
    tag: 1_BR_20250729-1347_d3b5965
  imagePullSecrets: []
  labels: {}
  name: wise-certificates-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
