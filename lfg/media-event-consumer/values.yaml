application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/observers/MediaEventConsumer.js
  env:
    DB_HOST: db-writer-lfg.dev.nivodaapi.net
    DB_NAME: wisecertificates
    DB_PASSWORD: rQrlEC17pboABB4vKNuB
    DB_PORT: '5432'
    DB_USERNAME: integrations
    INTEGRATIONS_BASE_URL: https://integrations-lfg.dev.nivodaapi.net
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: wise-media-consumer-lfg
    <PERSON>AFKA_MAIN_EVENT: WiseMediaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    PORT: '4000'
    SERVER_HOST: https://wise-certificates-lfg.dev.nivodaapi.net
    USE_KAFKA: 'true'
  environment: lfg
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/wise-certificates
    tag: 1_BR_20250729-1416_2792157
  imagePullSecrets: []
  labels: {}
  name: media-event-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
