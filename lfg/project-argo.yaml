apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: lfg
  namespace: argocd
spec:
  description: "Project for lfg environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: lfg 
  roles:
    - name: admin
      policies:
        - p, proj:lfg:admin, applications, *, proj:lfg, allow
        - p, proj:lfg:admin, clusters, *, *, allow
        - p, proj:lfg:admin, repositories, *, *, allow
      groups:
        - admins
