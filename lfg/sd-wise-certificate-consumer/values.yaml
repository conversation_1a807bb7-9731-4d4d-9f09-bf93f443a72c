application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/consumer.js
  env:
    DATABASE_URL: postgresql://integrations:<EMAIL>/certificates
    EXPOSE_SWAGGER_API: 'true'
    GCAL_VERSION_2: 'true'
    GSI_API_TOKEN: 9ED5DC41FA184AAB8458B5DADAF07643
    HOST: 0.0.0.0
    HRD_API_TOKEN: b72ea4e6-30c1-4fb5-a8c6-bbcaa4d203cf
    HRD_VERSION_2: 'true'
    IGI_VERSION_2: 'true'
    JWT_SECRET: 3a5fada2cec90122dff179e7d51ab8deab5b70a1d7f5605348cb87f7b9d4ef82c4350ae14a5952e9a9691ace1a93e441e1d6d7d7096a72687af4ea7d86353105
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_ACCESS_KEY: ********************
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: SD-wise-certificates
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: lfg-wise-consumer
    KAFKA_PASSWORD: nivoda123
    KAFKA_PRODUCER_TOPIC: testtopic
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SECRET_ACCESS_KEY: /Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    PASS: Nivoda123
    PORT: '4444'
    S3_BUCKET: nivoda-masked-pdfs
    S3_SECRET: 2Dmpb0D8L8JFyTiQFJmM8WFLQ3uXwxnTyCnU3Nx1
    SQS_CERTIFICATE_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo
    USER: Nivoda
    WISE_CERTIFICATE_HOST: http://wise-verifications
  environment: lfg
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/certificates
    tag: 1_BR_20250725-1857_d445ef2
  imagePullSecrets: []
  labels: {}
  name: sd-wise-certificate-consumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
