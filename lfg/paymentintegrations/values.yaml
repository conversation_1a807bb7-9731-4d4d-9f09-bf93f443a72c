application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    API_GATEWAY: "http://apigateway"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "http://certificates"
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    DB_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/d6ruvqcqduhvn5
    HOST: "https://website-lfg.dev.nivodaapi.net/"
    KEYCLOAK_ADMIN_SERVER_URL: "http://keycloak/auth"
    KEYCLOAK_SERVER_URL: "http://keycloak/auth"
    MARKET_PAY_NIVODA_SERVICE: "http://marketpay"
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_SQL_LOGGING: "false"
    NODE_ENV: "production"
    integration_service: "http://integrations/graphql-admin"
    graphql_service: "http://integrations/graphql"
    PGSSLMODE: "no-verify"
    port: "4002"
    REDIS_TLS_URL: "rediss-lfg-master.lfg.svc.cluster.local:6379"
    REDIS_URL: "redis-master.lfg.svc.cluster.local:6379"
    REDIS_URI_BULL: "redis-master.lfg.svc.cluster.local:6379"
    S3_IMAGE_BUCKET: "nivoda-staging"
    SERVER_HOST: "https://website-lfg.dev.nivodaapi.net"
    SLACK_OUTGOING: "*****************************************************************************"
    TAYLOR_HART_WEBHOOK_URL: "http://demo7656881.mockable.io/taylor"
    TWILIO_RATE_LIMIT_IDENTIFIER: "end_user_phone_number"
    USE_NETSUITE: "true"
    USE_XERO_DEMO: "true"
    WDC_ENV: "staging"
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: "website-lfg.dev.nivodaapi.net"
    pool_max: "500"
    pool_acquire: "30000"
    idle_in_transaction_session_timeout: "400000"
    QUEUE_ENABLED: "true"
    CREDIT_CHARGE: "2.5"
    KAFKA_ENABLED: "true"
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_USERNAME: "nivoda"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
    ADMIN_USER: "<EMAIL>"
    ADMIN_PASS: "Nivoda123"
    ignore_permissions: "true"
    allowed_cors: "*"
    STRIPE_AUTH_KEY: "jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c="
    STRIPE_SECRET_KEY_AUD: "sk_test_51MTu2wGUaRmKzM5u71f3mvvDhpRyeAGT6HlSVc1Yn5ydUjCWB4b1ZKD7eWlzaWslbvzXWRePvkXWg64uD7mCmGox00LFE7D7e9"
    STRIPE_SECRET_KEY_EUR: "sk_test_51MP4ykDi100pd0KfVbN8HbnIFe3LZR6uHUc8XSw2dFUCVrazhBm22LZssM8BWjjGVaxSmZXPYjgoCZ6Y43QCghnW00DJaKEZOb"
    STRIPE_SECRET_KEY_UK: "sk_test_51Cwt0FJ1nXM93VFF8pV0UAV8mDPbWZWFcMXFMONl42OFtzke8qOGIkgBBDUoextFV43nm2UO1pwS0v5atwD6mVWQ00xquVt1lE"
    STRIPE_SECRET_KEY_US: "sk_test_51LzdVWEHMBNJiNpatUen6W4KoXEzDRHh44i9jZK4s8vyawz4QxDuIIVm18SQJG6gtw1aq0HkLAfJ0CV1FdRY05Jr00Za3oljWm"
  environment: lfg
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/paymentintegrations
    tag: 1_BR_20241227-1715_82ae0ff
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/stripe_payment/health_check
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: paymentintegrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /api/v1/stripe_payment/health_check
      port: 4002
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4002
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: lfg
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: paymentintegrations-lfg.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
