terraform {
  source = "*****************:nivoda/infrastructure-modules.git//rds?ref=staging"
  # source = "/Users/<USER>/Documents/projects/infrastructure-modules/rds"
}

include "root" {
  path = find_in_parent_folders("root.hcl")
}

include "env" {
  path           = find_in_parent_folders("env.hcl")
  expose         = true
  merge_strategy = "no_merge"
}

inputs = {

  environment         = include.env.locals.env

  private_subnets  = ["subnet-032241fb40076b38c","subnet-0cb5bda0e76358acd","subnet-0042c02cafda3c721"]
  vpc_id   = "vpc-01efd5d22ad65e0fb"
  snapshot_identifier = "arn:aws:rds:eu-west-2:633477234672:cluster-snapshot:automation-db-3feb2025"
  instance_class = "db.t4g.large"
}

