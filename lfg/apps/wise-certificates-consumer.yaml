apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: wise-certificates-consumer-lfg
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v7
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/lfg/wise-certificates-consumer/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'lfg'
  project: lfg