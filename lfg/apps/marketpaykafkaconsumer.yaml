apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: marketpaykafkaconsumer-lfg
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v4
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/lfg/marketpaykafkaconsumer/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'lfg'
  project: lfg