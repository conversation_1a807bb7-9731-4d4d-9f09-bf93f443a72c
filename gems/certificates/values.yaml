application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    AWS_ACCESS_KEY_ID: ********************
    AWS_SECRET_ACCESS_KEY: oUHqei2VzSt53hQ4cvUbdny2VzH6kt/RESGuGMTL
    CERTIFICATE_EVENT_URL: http://certificate-events-lambda
    DATABASE_URL: postgresql://integrations:<EMAIL>/certificates
    EXPOSE_SWAGGER_API: 'true'
    GCAL_VERSION_2: 'true'
    GEMSTONE_SERVICE_HOST: https://gemstonesprocessor-gems.dev.nivodaapi.net
    HOST: 0.0.0.0
    HRD_VERSION_2: 'true'
    IGI_VERSION_2: 'true'
    KAFKA_ACCESS_KEY: ********************
    KAFKA_AUTHORIZATION_IDENTITY: AIDAZG7RPP7YBIEFKFLUM
    KAFKA_BROKER: b-1.devkafkacluster.8v3q4h.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.8v3q4h.c3.kafka.eu-west-2.amazonaws.com:9096,b-3.devkafkacluster.8v3q4h.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: testtopic
    KAFKA_PASSWORD: nivoda123
    KAFKA_PRODUCER_TOPIC: testtopic
    KAFKA_SECRET_ACCESS_KEY: /Z8hulef1XK09gncnJAJ28PXQnysRD9wVaeGNkj7
    KAFKA_USERNAME: nivoda
    NODE_ENV: staging
    PORT: '4444'
    S3_BUCKET: nivoda-masked-pdfs
    S3_SECRET: 2Dmpb0D8L8JFyTiQFJmM8WFLQ3uXwxnTyCnU3Nx1
  environment: gems
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/certificates
    tag: 1_BR_20250131-1842_0339502
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 4444
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: certificates
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /
      port: 4444
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 4444
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: gems
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: certificates-gems.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
