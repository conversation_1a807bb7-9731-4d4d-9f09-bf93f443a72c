application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    AWS_ACCESS_KEY_ID: ********************
    AWS_SECRET_ACCESS_KEY: VXA2imvOhJOVniTmFPYGxrSGuHcMXcYONTQr1JlC
    HOST: 0.0.0.0
    IMAGE_BUCKET: dev-nivoda-staging
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_CONSUMER_TOPIC: MediaProcessorEvents
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: video-processor-gems
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    MAX_MESSAGES_COUNT: '2'
    MP4_PROCESSING_ENABLED: 'true'
    PLATFORM_URL: http://integrations/graphql-admin
    RUN_FFMPEG_INSTALLER: 'false'
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/676625059805/nivoda-videos-new.fifo
    USE_KAFKA: 'true'
  environment: gems
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/videoprocessor
    tag: 1_BR_20250622-1613_feef5e4
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 5000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: videoprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 5000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 200m
      memory: 800Mi
    requests:
      cpu: 200m
      memory: 800Mi
  securityContext: {}
  service:
    port: 5000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: gems
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: videoprocessor-gems.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
