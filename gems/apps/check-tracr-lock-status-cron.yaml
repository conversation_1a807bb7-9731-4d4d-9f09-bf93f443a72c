apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: check-tracr-lock-status-cron-gems
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: job-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: job-chart
      targetRevision: v8
      helm:
        releaseName: job-chart
        valueFiles:
          - $valuesRepo/gems/check-tracr-lock-status-cron/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'gems'
  project: gems