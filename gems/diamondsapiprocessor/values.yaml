application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ADMIN_PASS: Nivoda123
    ADMIN_USER: <EMAIL>
    AWS_REGION: eu-west-1
    CERTIFICATE_HOST: https://certificates-gems.dev.nivodaapi.net
    DATABASE_URL: postgresql://integrations:<EMAIL>/gems
    DELTA_PROCESSING: 'false'
    DELTA_PROCESSING_FOR_ALL_SUPPLIER: 'true'
    DELTA_PROCESSING_VERSION_2: 'true'
    GRAPHQL_INTERNAL_TOKEN: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjBjZmJlODhhLTQwOGEtNDljYS04MjA4LWNlZjQzNWU4ZDdjOSIsInJvbGUiOiJBRE1JTiIsInN1YnR5cGUiOiJQTEFURk9STV9BTkRfQ0ZNIiwiY291bnRyeSI6IkdCIiwicHQiOiJERUZBVUxUIiwiaWYiOiIiLCJjaWQiOiIzYmUxMWU2ZS0yN2E2LTQ0ZmEtYmYxNi0yOTg5ZDg2Yzg5MjAiLCJnZW9fY291bnRyeSI6IklOIiwiYXBpIjpmYWxzZSwiYXBpX2MiOmZhbHNlLCJhcGlfaCI6ZmFsc2UsImFwaV9vIjpmYWxzZSwiYXBpX3IiOmZhbHNlLCJpYXQiOjE3MjU3OTMxOTIsImV4cCI6MTc1NzMyOTE5Mn0.g_g8ry4MUJHzN0kDm96QCAajW13kMexGoCCcMJHc_zA
    HISTORIC_STONES_QUEUE: HISTORIC_STONES_MAIN_QUEUE
    NODE_ENV: staging
    PLATFORM_URL: https://integrations-gems.dev.nivodaapi.net/graphql-admin
    PROD_DATABASE_URL: postgresql://integrations:<EMAIL>/gems
    PROXY_URL: *************
    QUEUE_ENABLED: 'false'
    REDIS_URI_BULL: redis://redis-master.gems.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.gems.svc.cluster.local:6379
    RUN_INITIALLY: 'true'
    S3_KEY: ASIAZG7RPP7YACHQFFF6
    S3_SECRET: bvF5GsMumZl5BrNku7HuIYy94I3hXeW2BDDOSrFI
    S3_SUPPLIER_FILES_BUCKET: nivoda-staging
    SAVE_UNMAPPED_VALUES: 'true'
    SLACK_SEC_TOKEN: ****************************************************************************
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo
    WDC_ENV: staging
  environment: gems
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/diamondsapiprocessor
    tag: 1_BR_20250506-1226_a06af4c3
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3004
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: diamondsapiprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3004
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3004
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: gems
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: diamondsapiprocessor-gems.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
