apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: gems
  namespace: argocd
spec:
  description: "Project for gems environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: gems 
  roles:
    - name: admin
      policies:
        - p, proj:gems:admin, applications, *, proj:gems, allow
        - p, proj:gems:admin, clusters, *, *, allow
        - p, proj:gems:admin, repositories, *, *, allow
      groups:
        - admins
