application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node observers/LongRunningConsumer.js
  env:
    ACCOUNTING_NETSUITE_URL: https://accounting-service-3197a576c6503f9f.onporter.run
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: https://dev-gateway.nivodaapi.net
    CERTIFICATE_HOST: https://certificates-gems.dev.nivodaapi.net
    DATABASE_URL: postgresql://integrations:<EMAIL>/gems-13feb
    DB_URL: postgresql://integrations:<EMAIL>/gems-13feb
    DOWNLOAD_GEMSTONE_CERTIFICATES: 'true'
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/gems-13feb
    GEMSTONES_SERVICE_STAGING: https://gemstonesprocessor-gems.dev.nivodaapi.net
    HOST: https://website-gems.dev.nivodaapi.net/
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: integrations-app-gems-env-long-running-group
    KAFKA_HIGH_PRIORITY_EVENT: HighPriorityEvents
    KAFKA_MAIN_EVENT: HighPriorityEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-gems.dev.nivodaapi.net/auth
    KEYCLOAK_SERVER_URL: https://keycloak-gems.dev.nivodaapi.net/auth
    MARKET_PAY_NIVODA_SERVICE: https://marketpay-gems.dev.nivodaapi.net
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda.invoices
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: staging
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    OTEL_SERVICE_NAME: integrations-gemstones
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    REDIS_TLS_URL: redis://redis-master.gems.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.gems.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.gems.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-gems.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-gems.dev.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '50'
  environment: gems
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250206-1236_ae349439c
  imagePullSecrets: []
  labels: {}
  name: longrunningtasksconsumer
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
