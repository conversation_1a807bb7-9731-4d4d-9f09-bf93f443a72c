apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: memo
  namespace: argocd
spec:
  description: "Project for memo environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: memo 
  roles:
    - name: admin
      policies:
        - p, proj:memo:admin, applications, *, proj:memo, allow
        - p, proj:memo:admin, clusters, *, *, allow
        - p, proj:memo:admin, repositories, *, *, allow
      groups:
        - admins
