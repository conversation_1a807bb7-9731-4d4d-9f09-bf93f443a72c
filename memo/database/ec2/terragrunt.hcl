terraform {
  source = "*****************:nivoda/infrastructure-modules.git//ec2?ref=v0.0.7"
  # source = "/Users/<USER>/Documents/projects/infrastructure-modules/ec2"
}

include "root" {
  path = find_in_parent_folders("root.hcl")
}

include "env" {
  path           = find_in_parent_folders("env.hcl")
  expose         = true
  merge_strategy = "no_merge"
}

inputs = {
  metadata = [{
    name          = "database"
    ami_id        = "ami-0e0e054d959757b8c"
    instance_type = "t4g.medium"
    subnet_id     = "subnet-032241fb40076b38c"
    vpc_id        = "vpc-01efd5d22ad65e0fb"
    policy_arns   = ["arn:aws:iam::aws:policy/AdministratorAccess"]
    spot_instance = true
    volume_size   = 60
    ingress_rules = [
      {
        from_port        = 0
        to_port          = 0
        protocol         = "-1"
        cidr_blocks      = ["10.0.0.0/8"]
      }
    ]
    egress_rules = [{
      from_port        = 0
      to_port          = 0
      protocol         = "-1"
      cidr_blocks      = ["0.0.0.0/0"]

    }]
}]
  key_name = "pradipkundu"
  env = include.env.locals.env

}
