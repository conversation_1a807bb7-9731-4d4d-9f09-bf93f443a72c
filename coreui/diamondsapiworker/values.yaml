application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node src/queue_worker.js
  env:
    ADMIN_PASS: Nivoda123
    ADMIN_USER: <EMAIL>
    AWS_REGION: eu-west-1
    CERTIFICATE_HOST: http://certificates
    DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DELTA_PROCESSING: 'false'
    DELTA_PROCESSING_FOR_ALL_SUPPLIER: 'true'
    DELTA_PROCESSING_VERSION_2: 'true'
    GRAPHQL_INTERNAL_TOKEN: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjJhNjdjNjVkLWY5ZTEtNDQyMC1hZTEzLTFjNjA4YTcyZGZlYiIsInJvbGUiOiJBRE1JTiIsInN1YnR5cGUiOiJQTEFURk9STV9BTkRfQ0ZNIiwiY291bnRyeSI6IkdCIiwicHQiOiJERUZBVUxUIiwiaWYiOiIiLCJjaWQiOiIzYmUxMWU2ZS0yN2E2LTQ0ZmEtYmYxNi0yOTg5ZDg2Yzg5MjAiLCJnZW9fY291bnRyeSI6IkdCIiwiYXBpIjpmYWxzZSwiYXBpX2MiOmZhbHNlLCJhcGlfaCI6ZmFsc2UsImFwaV9vIjpmYWxzZSwiYXBpX3IiOmZhbHNlLCJpYXQiOjE3MjUzNDE3NTYsImV4cCI6MTcyNTQyODE1Nn0.7hOrbE_-1lbO3Je34JZjQihZ1X_vJmyx_bHLI-mIjlQ
    HISTORIC_STONES_QUEUE: HISTORIC_STONES_MAIN_QUEUE
    NODE_ENV: staging
    PLATFORM_URL: http://integrations/graphql-admin
    PROD_DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    PROXY_URL: *************
    QUEUE_ENABLED: 'false'
    REDIS_URI_BULL: redis://redis-master.coreui.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.coreui.svc.cluster.local:6379
    RUN_INITIALLY: 'true'
    S3_KEY: ASIAZG7RPP7YACHQFFF6
    S3_SECRET: bvF5GsMumZl5BrNku7HuIYy94I3hXeW2BDDOSrFI
    S3_SUPPLIER_FILES_BUCKET: nivoda-staging
    SAVE_UNMAPPED_VALUES: 'true'
    SLACK_SEC_TOKEN: ****************************************************************************
    SQS_QUEUE_URL: https://sqs.eu-west-2.amazonaws.com/************/diamondQueueStaging.fifo
    WDC_ENV: staging
  environment: coreui
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/diamondsapiprocessor
    tag: 1_BR_20250318-1732_3c583c40
  imagePullSecrets: []
  labels: {}
  name: diamondsapiworker
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
