application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    CERTIFICATE_HOST: http://certificates
    CONCURRENT_SUPPLIERS: '1'
    DATABASE_URL: postgresql://integrations:<EMAIL>/gemstones_processor
    ENABLE_CRON: 'false'
    GRAPHQL_INTERNAL_TOKEN: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjBjZmJlODhhLTQwOGEtNDljYS04MjA4LWNlZjQzNWU4ZDdjOSIsInJvbGUiOiJBRE1JTiIsInN1YnR5cGUiOiJQTEFURk9STV9BTkRfQ0ZNIiwiY291bnRyeSI6IkdCIiwicHQiOiJERUZBVUxUIiwiaWYiOiIiLCJjaWQiOiIzYmUxMWU2ZS0yN2E2LTQ0ZmEtYmYxNi0yOTg5ZDg2Yzg5MjAiLCJnZW9fY291bnRyeSI6IklOIiwiYXBpIjpmYWxzZSwiYXBpX2MiOmZhbHNlLCJhcGlfaCI6ZmFsc2UsImFwaV9vIjpmYWxzZSwiYXBpX3IiOmZhbHNlLCJpYXQiOjE3MjU3OTMxOTIsImV4cCI6MTc1NzMyOTE5Mn0.g_g8ry4MUJHzN0kDm96QCAajW13kMexGoCCcMJHc_zA
    NODE_ENV: staging
    PLATFORM_URL: http://integrations/graphql-admin
    PORT: '3005'
    PROCESS_API_STOCK_FREQUENCY_HOURS: '2'
    REDIS_URL: redis://redis-master.coreui.svc.cluster.local:6379
    RUN_INITIALLY: 'true'
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SLACK_TOKEN: asdfasdf
  environment: coreui
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/gemstonesprocessor
    tag: 1_BR_20250318-1725_ca54a03
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: gemstonesprocessor
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health
      port: 3005
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3005
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: coreui
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: gemstonesprocessor-coreui.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
