apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: redis-coreui
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  project: coreui 
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: redis
    - repoURL: "https://charts.bitnami.com/bitnami"
      targetRevision: "20.1.4"
      chart: redis
      helm:
        releaseName: redis
        valueFiles:
          - $valuesRepo/coreui/redis/values.yaml
  destination:
    name: 'in-cluster'
    namespace: coreui     