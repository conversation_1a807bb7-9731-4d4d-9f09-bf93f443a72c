apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: apigateway-coreui
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: web-chart
    - repoURL: 'ssh://*****************/nivoda/app-secrets.git'
      targetRevision: dev
      ref: secretsrepo
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: web-chart
      targetRevision: v8
      helm:
        releaseName: web-chart
        valueFiles:
          - $valuesRepo/coreui/apigateway/values.yaml
          - $secretsrepo/coreui/apigateway/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'coreui'
  project: coreui