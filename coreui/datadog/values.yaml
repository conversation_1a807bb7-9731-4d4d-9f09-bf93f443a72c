agents:
  additionalLabels: {}
  affinity: {}
  containers:
    agent:
      env:
        - name: DD_LOGS_CONFIG_AUTO_MULTI_LINE_EXTRA_PATTERNS
          value: '(info|error|warn): (\w{3}-\d{1,2}-\d{4} \d{1,2}:\d{1,2}:\d{1,2}).*'
        - name: DD_LOGS_CONFIG_K8S_CONTAINER_USE_FILE
          value: 'true'
      envDict: {}
      envFrom: []
      healthPort: 5555
      livenessProbe:
        failureThreshold: 6
        initialDelaySeconds: 15
        periodSeconds: 15
        successThreshold: 1
        timeoutSeconds: 5
      ports: []
      readinessProbe:
        failureThreshold: 6
        initialDelaySeconds: 15
        periodSeconds: 15
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          memory: 256Mi
        requests:
          cpu: 202m
          memory: 256Mi
      securityContext: {}
    initContainers:
      resources: {}
    processAgent:
      enabled: true
      env: []
      envDict: {}
      envFrom: []
      ports: []
      processCollection: true
      resources: {}
      securityContext: {}
    securityAgent:
      env: []
      envDict: {}
      envFrom: []
      logLevel: null
      ports: []
      resources: {}
    systemProbe:
      enableOOMKill: true
      env: []
      envDict: {}
      envFrom: []
      logLevel: null
      ports: []
      resources: {}
      securityContext:
        capabilities:
          add:
            - SYS_ADMIN
            - SYS_RESOURCE
            - SYS_PTRACE
            - NET_ADMIN
            - NET_BROADCAST
            - NET_RAW
            - IPC_LOCK
        privileged: false
    traceAgent:
      env: []
      envDict: {}
      envFrom: []
      livenessProbe:
        initialDelaySeconds: 15
        periodSeconds: 15
        timeoutSeconds: 5
      logLevel: null
      ports: []
      resources: {}
      securityContext: {}
  customAgentConfig: {}
  daemonsetAnnotations: {}
  dnsConfig: {}
  enabled: true
  image:
    digest: ''
    doNotCheckTag: null
    name: agent
    pullPolicy: IfNotPresent
    pullSecrets: []
    repository: null
    tag: latest
    tagSuffix: ''
  localService:
    forceLocalServiceEnabled: false
    overrideName: ''
  networkPolicy:
    create: false
  nodeSelector: {}
  podAnnotations: {}
  podLabels: {}
  podSecurity:
    allowedUnsafeSysctls: []
    apparmor:
      enabled: true
    apparmorProfiles:
      - runtime/default
      - unconfined
    capabilities:
      - SYS_ADMIN
      - SYS_RESOURCE
      - SYS_PTRACE
      - NET_ADMIN
      - NET_BROADCAST
      - NET_RAW
      - IPC_LOCK
      - AUDIT_CONTROL
      - AUDIT_READ
    defaultApparmor: runtime/default
    podSecurityPolicy:
      create: false
    privileged: false
    seLinuxContext:
      rule: MustRunAs
      seLinuxOptions:
        level: s0
        role: system_r
        type: spc_t
        user: system_u
    seccompProfiles:
      - runtime/default
      - localhost/system-probe
    securityContextConstraints:
      create: false
    volumes:
      - configMap
      - downwardAPI
      - emptyDir
      - hostPath
      - secret
  priorityClassCreate: false
  priorityClassName: null
  priorityClassValue: **********
  priorityPreemptionPolicyValue: PreemptLowerPriority
  rbac:
    automountServiceAccountToken: true
    create: true
    serviceAccountAnnotations: {}
    serviceAccountName: default
  revisionHistoryLimit: 10
  shareProcessNamespace: false
  tolerations:
    - effect: NoSchedule
      key: removable
      operator: Equal
      value: 'true'
    - effect: NoSchedule
      key: porter.run/workload-kind
      operator: Equal
      value: system
    - effect: NoSchedule
      key: spot-instance
      operator: Equal
      value: 'true'
    - effect: NoSchedule
      key: search-consumer
      operator: Equal
      value: 'true'
    - effect: NoSchedule
      key: critical
      operator: Equal
      value: 'true'
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 10%
    type: RollingUpdate
  useConfigMap: null
  useHostNetwork: false
  volumeMounts: []
  volumes: []
clusterAgent:
  additionalLabels: {}
  admissionController:
    enabled: true
    failurePolicy: Ignore
    mutateUnlabelled: true
    remoteInstrumentation:
      enabled: false
  advancedConfd: {}
  affinity: {}
  command: []
  confd: {}
  containers:
    clusterAgent:
      securityContext:
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
  createPodDisruptionBudget: false
  datadog_cluster_yaml: {}
  deploymentAnnotations: {}
  dnsConfig: {}
  enabled: true
  env: []
  envDict: {}
  envFrom: []
  healthPort: 5556
  image:
    digest: ''
    doNotCheckTag: null
    name: cluster-agent
    pullPolicy: IfNotPresent
    pullSecrets: []
    repository: null
    tag: 7.66.1
  livenessProbe:
    failureThreshold: 6
    initialDelaySeconds: 15
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  metricsProvider:
    aggregator: avg
    createReaderRbac: true
    enabled: false
    service:
      port: 8443
      type: ClusterIP
    useDatadogMetrics: false
    wpaController: false
  networkPolicy:
    create: false
  nodeSelector: {}
  podAnnotations: {}
  podSecurity:
    podSecurityPolicy:
      create: false
    securityContextConstraints:
      create: false
  priorityClassName: null
  rbac:
    automountServiceAccountToken: true
    create: true
    flareAdditionalPermissions: true
    serviceAccountAnnotations: {}
    serviceAccountName: default
  readinessProbe:
    failureThreshold: 6
    initialDelaySeconds: 15
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  replicas: 1
  resources: {}
  revisionHistoryLimit: 10
  securityContext: {}
  shareProcessNamespace: false
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  token: ''
  tokenExistingSecret: ''
  tolerations: []
  useHostNetwork: false
  volumeMounts: []
  volumes: []
clusterChecksRunner:
  additionalLabels: {}
  affinity: {}
  createPodDisruptionBudget: false
  deploymentAnnotations: {}
  dnsConfig: {}
  enabled: true
  env: []
  envDict: {}
  envFrom: []
  healthPort: 5557
  image:
    digest: ''
    name: agent
    pullPolicy: IfNotPresent
    pullSecrets: []
    tag: latest
    tagSuffix: ''
  livenessProbe:
    failureThreshold: 6
    initialDelaySeconds: 15
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  networkPolicy:
    create: false
  nodeSelector: {}
  podAnnotations: {}
  ports: []
  priorityClassName: null
  rbac:
    automountServiceAccountToken: true
    create: true
    dedicated: false
    serviceAccountAnnotations: {}
    serviceAccountName: default
  readinessProbe:
    failureThreshold: 6
    initialDelaySeconds: 15
    periodSeconds: 15
    successThreshold: 1
    timeoutSeconds: 5
  replicas: 2
  resources: {}
  revisionHistoryLimit: 10
  securityContext: {}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  tolerations: []
  volumeMounts: []
  volumes: []
clusterId: 2102
commonLabels: {}
currentCluster:
  service:
    is_aws: true
    is_do: false
    is_gcp: false
datadog:
  apiKey: ********************************
  apiKeyExistingSecret: null
  apm:
    enabled: true
    hostSocketPath: /var/run/datadog/
    port: 8126
    portEnabled: true
    socketEnabled: false
    socketPath: /var/run/datadog/apm.socket
    useSocketVolume: false
  appKey: null
  appKeyExistingSecret: null
  checksCardinality: null
  checksd: {}
  clusterChecks:
    enabled: true
    shareProcessNamespace: false
  clusterName: nivoda-production-cluster
  clusterTagger:
    collectKubernetesTags: false
  collectEvents: true
  confd:
    http_check.d: |-
      init_config:
      instances:
      - name: Heroku Staging - Shopify
        url: https://wdc-intg-customer-staging.herokuapp.com/ 
    nginx_ingress_controller: |-
      init_config:
      instances:
        - nginx_status_url: 'http://nginx-ingress-ingress-nginx-controller-metrics.ingress-nginx.svc.cluster.local:10254/healthz'
          prometheus_url: 'http://prometheus-kube-state-metrics.monitoring.svc.cluster.local:8080/metrics'
    postgres.yaml: |-
      init_config:
      instances:
        - dbm: true
          host: 'prod-db-main-writer.nivodaapi.net'
          username: datadog_monitoring
          password: 'datadog123'
  containerExclude: image:^.*datadog.*$  kube_namespace:^kube.*$ kube_namespace:^monitoring$
  containerExcludeLogs: >-
    kube_namespace:cert-manager kube_namespace:ingress-nginx
    kube_namespace:kube-system kube_namespace:monitoring
    kube_namespace:porter-agent-system image:^.*hubspot.*$ name:^.*hubspot.*$
  containerExcludeMetrics: >-
    kube_namespace:cert-manager kube_namespace:ingress-nginx
    kube_namespace:kube-system kube_namespace:monitoring
    kube_namespace:porter-agent-system
  containerRuntimeSupport:
    enabled: true
  criSocketPath: null
  dd_url: null
  dockerSocketPath: null
  dogstatsd:
    hostSocketPath: /var/run/datadog
    nonLocalTraffic: true
    originDetection: false
    port: 8125
    socketPath: /var/run/datadog/dsd.socket
    tagCardinality: low
    tags: []
    useHostPID: false
    useHostPort: true
    useSocketVolume: false
  env: {}
  envDict: {}
  envFrom: []
  excludePauseContainer: true
  expvarPort: 6000
  helmCheck:
    collectEvents: false
    enabled: false
    valuesAsTags: {}
  hostVolumeMountPropagation: None
  ignoreAutoConfig: []
  kubeStateMetricsCore:
    annotationsAsTags: {}
    collectSecretMetrics: true
    collectVpaMetrics: false
    enabled: true
    ignoreLegacyKSMCheck: true
    labelsAsTags: {}
    rbac:
      create: true
    useClusterCheckRunners: false
  kubeStateMetricsEnabled: true
  kubeStateMetricsNetworkPolicy:
    create: false
  kubelet:
    host:
      valueFrom:
        fieldRef:
          fieldPath: status.hostIP
    hostCAPath: null
    podLogsPath: null
    tlsVerify: null
  kubernetesEvents:
    collectedEventTypes:
      - kind: Pod
        reasons:
          - Failed
          - BackOff
          - Unhealthy
          - FailedScheduling
          - FailedMount
          - FailedAttachVolume
      - kind: Node
        reasons:
          - TerminatingEvictedPod
          - NodeNotReady
          - Rebooted
          - HostPortConflict
      - kind: CronJob
        reasons:
          - SawCompletedJob
    unbundleEvents: false
  leaderElection: true
  logLevel: INFO
  logs:
    autoMultiLineDetection: true
    containerCollectAll: true
    containerCollectUsingFiles: true
    enabled: true
  logs_config:
    auto_multi_line_extra_patterns:
      - (info|error|warn): (\w{3}-\d{1,2}-\d{4} \d{1,2}:\d{1,2}:\d{1,2}).*
  namespaceLabelsAsTags: {}
  networkMonitoring:
    enabled: false
  networkPolicy:
    cilium:
      dnsSelector:
        toEndpoints:
          - matchLabels:
              k8s:io.kubernetes.pod.namespace: kube-system
              k8s:k8s-app: kube-dns
    create: false
    flavor: kubernetes
  nodeLabelsAsTags: {}
  orchestratorExplorer:
    container_scrubbing:
      enabled: false
    enabled: true
  osReleasePath: /etc/os-release
  otlp:
    receiver:
      protocols:
        grpc:
          enabled: false
          endpoint: 0.0.0.0:4317
          useHostPort: true
        http:
          enabled: false
          endpoint: 0.0.0.0:4318
          useHostPort: true
  podAnnotationsAsTags: {}
  podLabelsAsTags: {}
  processAgent:
    enabled: true
    processCollection: true
    processDiscovery: true
    stripProcessArguments: false
  prometheusScrape:
    additionalConfigs: []
    enabled: false
    serviceEndpoints: false
    version: 2
  remoteConfiguration:
    enabled: false
  secretAnnotations: {}
  secretBackend:
    arguments: null
    command: null
    enableGlobalPermissions: true
    roles: []
    timeout: null
  securityAgent:
    compliance:
      checkInterval: 20m
      configMap: null
      enabled: false
    runtime:
      activityDump:
        cgroupDumpTimeout: 20
        cgroupWaitListSize: 0
        enabled: true
        pathMerge:
          enabled: false
        tracedCgroupsCount: 3
      enabled: false
      fimEnabled: false
      network:
        enabled: true
      policies:
        configMap: null
      syscallMonitor:
        enabled: false
  securityContext:
    runAsUser: 0
  serviceMonitoring:
    enabled: false
  serviceTopology:
    enabled: false
    serviceName: datadog-agent
  site: datadoghq.eu
  systemProbe:
    apparmor: unconfined
    bpfDebug: false
    btfPath: ''
    collectDNSStats: true
    conntrackInitTimeout: 10s
    conntrackMaxStateSize: 131072
    debugPort: 0
    enableConntrack: true
    enableDefaultKernelHeadersPaths: true
    enableDefaultOsReleasePaths: true
    enableOOMKill: false
    enableTCPQueueLength: false
    maxTrackedConnections: 131072
    mountPackageManagementDirs: []
    runtimeCompilationAssetDir: /var/tmp/datadog-agent/system-probe
    seccomp: localhost/system-probe
    seccompRoot: /var/lib/kubelet/seccomp
  tags: []
  useHostPID: true
datadog-crds:
  crds:
    datadogAgents: false
    datadogMetrics: true
    datadogMonitors: false
  fullnameOverride: ''
  global: {}
  nameOverride: ''
existingClusterAgent:
  clusterchecksEnabled: true
  join: false
  serviceName: null
  tokenSecretName: null
fullnameOverride: null
kube-state-metrics:
  affinity: {}
  autosharding:
    enabled: false
  collectors:
    certificatesigningrequests: true
    configmaps: true
    cronjobs: true
    daemonsets: true
    deployments: true
    endpoints: true
    horizontalpodautoscalers: true
    ingresses: true
    jobs: true
    limitranges: true
    mutatingwebhookconfigurations: true
    namespaces: true
    networkpolicies: true
    nodes: true
    persistentvolumeclaims: true
    persistentvolumes: true
    poddisruptionbudgets: true
    pods: true
    replicasets: true
    replicationcontrollers: true
    resourcequotas: true
    secrets: true
    services: true
    statefulsets: true
    storageclasses: true
    validatingwebhookconfigurations: true
    verticalpodautoscalers: false
    volumeattachments: true
  customLabels: {}
  extraArgs: []
  global: {}
  hostNetwork: false
  image:
    pullPolicy: IfNotPresent
    repository: quay.io/coreos/kube-state-metrics
    tag: v1.9.7
  imagePullSecrets: []
  kubeTargetVersionOverride: ''
  kubeconfig:
    enabled: false
    secret: null
  namespaceOverride: ''
  nodeSelector: {}
  podAnnotations: {}
  podDisruptionBudget: {}
  podSecurityPolicy:
    additionalVolumes: []
    annotations: {}
    enabled: false
  prometheus:
    monitor:
      additionalLabels: {}
      enabled: false
      honorLabels: false
      namespace: ''
  prometheusScrape: true
  rbac:
    create: true
    useClusterRole: true
  replicas: 1
  resources: {}
  securityContext:
    enabled: true
    fsGroup: 65534
    runAsGroup: 65534
    runAsUser: 65534
  selfMonitor:
    enabled: false
  service:
    annotations: {}
    loadBalancerIP: ''
    nodePort: 0
    port: 8080
    type: ClusterIP
  serviceAccount:
    annotations: {}
    create: true
    imagePullSecrets: []
    name: null
  tolerations:
    - effect: NoSchedule
      key: search-consumer
      operator: Equal
      value: 'true'
nameOverride: null
providers:
  aks:
    enabled: false
  eks:
    ec2:
      useHostnameFromFile: false
  gke:
    autopilot: false
    cos: false
registry: gcr.io/datadoghq
targetSystem: linux
