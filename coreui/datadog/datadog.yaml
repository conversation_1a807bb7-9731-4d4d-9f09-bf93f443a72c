apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: datadog-coreui
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  project: coreui 
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: datadog
    - repoURL: "https://helm.datadoghq.com"
      targetRevision: "3.118.3"
      chart: datadog
      helm:
        releaseName: datadog
        valueFiles:
          - $valuesRepo/coreui/datadog/values.yaml
  destination:
    name: 'in-cluster'
    namespace: coreui     