application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 4
    minReplicas: 4
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: https://accounting-service-3197a576c6503f9f.onporter.run
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_NIVODA_SERVICE: https://airwallex-coreui.nivodaapi.net
    ALLIANZ_NIVODA_SERVICE: https://allianz-coreui.nivodaapi.net
    ALLIANZ_POLICY_ID: '*********'
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_ENABLED: 'true'
    AUTHENTICATOR: KEYCLOAK
    AUTH_URL: https://fegateway-coreui.dev.nivodaapi.net
    BYPASSCALL: 'true'
    BYPASSWHATSAPP: 'true'
    CERTIFICATE_HOST: https://certificates-coreui.dev.nivodaapi.net
    CERT_REMOVAL_EVENTS: 'true'
    CREDIT_SAFE_CREDENTIALS: map[password:OXLY66kH-Q(ZIQRFM@Q5 username:<EMAIL>]
    DATABASE_URL: postgresql://integrations:<EMAIL>/coreui
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/coreui
    DB_URL: postgresql://integrations:<EMAIL>/coreui
    DD_AGENT_MAJOR_VERSION: '7'
    DD_API_KEY: ********************************
    DD_APPSEC_ENABLED: 'false'
    DD_DYNO_HOST: 'false'
    DD_ENV: coreui
    DD_IAST_ENABLED: 'false'
    DD_SERVICE: integrations-coreui
    DD_SITE: datadoghq.eu
    DD_TRACE_AGENT_URL: http://datadog:8126
    DIAMONDS_SERVICE_PROD: https://diamonds-coreui.dev.nivodaapi.net
    DIAMONDS_SERVICE_STAGING: https://diamonds-coreui.dev.nivodaapi.net
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/coreui
    FORCE_DD: 'true'
    GEMSTONES_SERVICE_STAGING: https://gemstonesprocessor-coreui.dev.nivodaapi.net
    GOOGLE_CLIENT_EMAIL: <EMAIL>
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

      '
    GOOGLE_PROJECT_ID: elaborate-truth-448803-c6
    HOST: https://website-coreui.nivodaapi.net/
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: integrations-app
    KAFKA_HIGH_PRIORITY_EVENT: NivodaEventsHighPriorityCoreUI
    KAFKA_MAIN_EVENT: NivodaEventsCoreUI
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEventsCoreUI
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: https://keycloak-coreui.dev.nivodaapi.net
    KEYCLOAK_CLIENTID: nivodaapp
    KEYCLOAK_REALM: Nivoda
    KEYCLOAK_SECRET: vvdGsyCIZh3PioqIdwVm0Glco51zaMPH
    KEYCLOAK_SERVER_URL: https://keycloak-coreui.dev.nivodaapi.net/
    LOG_IN_JSON: 'true'
    MARKET_PAY_NIVODA_SERVICE: https://marketpay-coreui.nivodaapi.net
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: production
    OTEL_EXPORTER_OTLP_ENDPOINT: http://signoz-otel-collector.signoz:4318/v1/traces
    OTEL_RESOURCE_ATTRIBUTES: service.name=integrations-coreui,service.version=1.0.0,deployment.environment=coreui
    OTEL_SERVICE_NAME: integrations-coreui
    OTEL_SERVICE_VERSION: 1.0.0
    PGSSLMODE: no-verify
    PORT: '3000'
    REDIS_TLS_URL: redis://redis-master.coreui.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.coreui.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.coreui.svc.cluster.local:6379
    REDSHIFT_DATABASE: bi_server
    REDSHIFT_HOST: ************
    REDSHIFT_PASSWORD: ahmed@1KNuB
    REDSHIFT_PORT: '5439'
    REDSHIFT_USER: ahmed
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SEARCH_SERVICE_ENABLED: 'true'
    SEARCH_SERVICE_TEXT_SEARCH_DISABLED: 'false'
    SEARCH_SERVICE_URL: http://searchservice.coreui.svc.cluster.local
    SERVER_HOST: https://website-coreui.nivodaapi.net
    SKIP_FEATURE_FLAG_SEARCH: enabled
    SKIP_TRANSACTION_FOR_QUERY: enabled
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_SID: **********************************
    TWILIO_TOKEN: 3ec4e8a4cad679bb3545b5adbe782e9c
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-coreui.nivodaapi.net
    ZENDESK_JWT_API_KEY: app_674d433e337dd8ccfa323cce
    ZENDESK_JWT_SECRET: GaKOgqXMCejR-4aBcP9tK37N8nWKMVAdGFaofa-Rn1ZgG7KBaf3fSENCvf_a3aonZqClfOR4cJlNqyUrNy7uoA
    idle_in_transaction_session_timeout: '400000'
    jwt_cert: MIICoTCCAYkCBgGCNFrRNTANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAluaXZvZGFhcHAwHhcNMjIwNzI1MDc1NDAzWhcNMzIwNzI1MDc1NTQzWjAUMRIwEAYDVQQDDAluaXZvZGFhcHAwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCo+EStKXJNwfJnWFK80ivOvShcRNHDn1MZOMz9dhB/fHdEzmfi+QXkbC1h0KEVkSnUPNc7QK203KXizoZdDUe/NcrVo2aZpRjW9haVmKAoVbMdDZC1MTJv2uR2Cn9FFtuEEUpnU3EPtG2mFjFLx9oYTmPfaElh6dEvdOnPAzneXS2CmHzb86B271Yi2Sktt++pG26d//zOPITqdZjYtlY6wsXltNXlCmj2n6RtQyTQzHIRhXcLP1z7ZeIg13I1KsfN0/tXfaD/xx/Pz7CN8+5RxgMVce0V8WU9gcbR7FDOq7XhYNYtJkp33CciTS698yPp04ZqpeTJ3cBMd9p7D6OjAgMBAAEwDQYJKoZIhvcNAQELBQADggEBABqq8cI/8+Eu5LDBJK/a67B5HkwZx/dcmZUp+EjXim4vOnbkPL7dvd3tRtkvJMaVkwePpRrZ/uCElOW5FFRL0rSjSqzkOyNMzn56brLrjiLH82z64i9AzFhEySLWgAFQwBdTzIlcqPXRxee3ECkwfTUYpaVgRRL4xOqQ03ng3ODYQKtwPBzPwV9LFfT0oRJibRAb8DqmvBqppkwPJkQ2hNqD7sB7X/OZwQYo3NfPNKNMAzg1XWINdBV3chpafjEHfHgUDodEffqb5/EspxZ20pGVHZ4udry9Et80ymH0/T2UUXm86Sw/6X220iOBRJwdkdraAi/X5cmLJ+yZzjlxlek=
    okta_client_id: 0oakqh8k926XUNwHR5d7
    okta_issuer: https://dev-32165027.okta.com/oauth2/default
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: coreui
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250729-1331_f0a793448f
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: integrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 4
  resources:
    limits:
      cpu: 300m
      ephemeral-storage: 4Gi
      memory: 3000Mi
    requests:
      cpu: 300m
      ephemeral-storage: 2Gi
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: coreui
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: integrations-coreui.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
