application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ENABLE_GRAPHQL_PLAYGROUND: 'true'
    GOOGLE_CLIENT_EMAIL: <EMAIL>
    GOOGLE_DRIVE_FOLDER_ID: 1GIR3GpjyXfU6naXLe6_h1KhG5jgqeBv1
    GOOGLE_PRIVATE_KEY: '-----BEGIN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCM0XqlocM3WjXi\nnh7kJQ0TS/aOIvVvqn2vRE/4wFroTKuikssLiuPhI07Hsh1xFgQ9HnMavkMJF/UO\nv4CXGypg/BRMsOgqg3F/HTkmhD3K0h3arWgVv/xWjeq138TF9bSFfwbnsOjPjYqc\n6U1umvJuWqI7zNgZ5Km7qfM1SxvLgO1ZZXPQvdPm+ZwJWIH4V5o9Uc2T+WD7nvEm\nr5Taa0UVmC1NXvqoDXWJxJmwz9QpAtuPesYdbzTNtopfB6HiGTr0IpjWs5afG1dj\nFlOkNcy850qphGm7CyC4P/MwdMXh1R15UhdqaDawkg3M2sRSSldsxFg1TFfSMVax\nZKIo6TxjAgMBAAECggEAC22ClQd8JrVQ8yue2SfymDLNdvp6TY4Z9gvGq6q8leaG\n9UIRbUXfuWkanxUi04cUX/tlHvH60bWagxBieKDRPx6l7RVcOfsBzm2KMkiAFUIY\n/yKcdEtoyINq3UikGOaYOE83Ob0/H3zel15NlODJNNBxIwY8MVm/toWTwarvPgQB\nJQVJ80rKc+voNhWBR1RSV7X00tOY0DVDV5BLtQrJv8h4UxrO7uLYgf+/bPEuPHfE\n4DUJKKOnSqN0Mv9co12NIBSgiM6mLdh0u5t6j54OKU6Ri/yJYdLkcy1E8BrBi0uQ\nryZsN/CMHRJ9aBDSMkVhRaS+ESE37fd85WpaM/rkGQKBgQDHFtNNJeFCM3sU7dyQ\np+hJmPYLDsoP1QL+GrNazYVOaKHP6xMDjPG5cujaOKCxaGGCmKaUl7SW7zf3eTZe\n0MH50eEt6HwELjVWHbQFLI3ChKVYH3t4mLKfE8QKjzTTWIPybGOW3n2YjxI0ammA\njpZ53SAlJXHrtOziJ4nYqNTaWQKBgQC1EnCzC8R9jAHUoJyuJr7+CvD/TLpF0x4H\nXh58K4klv0RJ0F9cg7zqhZolJKSGkzEPCBz6cuTC3rkpGhBVu5TLRYyxi8xtO9ky\nlMVJJNxMxX7R2ojpgvw+oweKIAZrlzLL86HUyZZVLZ/JeT83iLX7Nd78H44glVdg\n4WXvugc9GwKBgGxiErpnKtB9cj4cl/zwS9b1PM0cSgiAcFahdo6yYCYldCHa2ckP\nC/Fnizhxp5mtN5h/PgCa7idVJ+TDykOn5lH4Xlw9unYUKdkt5nkCnlINIG5vBSrM\nBkHv4sPU3m9IOtzZAQZ7LWjGSqTaYYEpHuZM6VkPn4swPd2IpNScZPRRAoGAMaCO\nNuRgxDAdSFOE3c5Mxn/lMXOsEfsc3dQT8uBTxH65jqmHmWmZ24UmjEuQtDmfVolY\nt+PJgwdcJEG6fGTfER9MwlSYgM/IZa6Uq8lof7oZWhbVM3rs+5XfOSIx4Eceavup\nPRLLGylYA/YxN11G00UeimdzosaLnkfokl2gIkUCgYBMoFkxgtbXXg0RgP4ASv7A\n/q8pOHri3JiDNOkURgqwTlODrELYRmN6htAPkfXIXBpx7sBkk6xmJb6995qkeaBH\ndKoBCCTm2nlIhSRyvLyMluVp0NEEh6kr3E/pIUqyeaycjnDpjcyqju3tGl0WfwZS\nt57ynqw9OCcizgmKyVRo9g==\n-----END
      PRIVATE KEY-----\n'
    GOOGLE_PRIVATE_KEY_ID: addf70e9c1d97124145af7a585e7c5d18c93ac37
    GOOGLE_PROJECT_ID: shopifydesignsync
    INTEGRATIONS_API_TOKEN: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI1NXBjMTBMVWdWVlhBVXdRWmhVWnd1VXY2Um56MVFBTm5OQm43TlVIOHFnIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iHbowHC-YzGqwd6p61VU2pDdTfgBsIstMUwGuIrr6xCnX8ijCTIztpc4NbHIh9CBYmDhVAMR5zadO2HHw9n584MG7Z7n_mCm6YZCDXim7r4XlKHLGoYTYtJnryZKKQmlYCo1M3BZvYkk5hcngpMM_SEdyiTfnfRrLCmI05keNSHW6l1M2jWl2v1dg_ay5ZjaoNuS6oSvITkI4yClawkJjWF5tXpJYtuYVQ0zAqzZ58daQNtU2cngsNr3giP1ZZbaiMXLMUpjO658qef7VoOesLbK983CQ2qqdt68eFjdvGH6SNN7qAIqEA3he4r7Lktu9-NhjGc2Re6qBcp2dLVP5Q
    INTEGRATIONS_API_URL: https://integrations-prod-alpha.nivodaapi.net/graphql
    JEWELLERY_DB_HOST: db-writer-lfg.dev.nivodaapi.net
    JEWELLERY_DB_NAME: jewellery-service
    JEWELLERY_DB_PASSWORD: rQrlEC17pboABB4vKNuB
    JEWELLERY_DB_PORT: '5432'
    JEWELLERY_DB_USERNAME: integrations
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: jewellery-service
    KAFKA_JEWELLERY_TOPIC: JewelryEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_USERNAME: nivoda
    NODE_ENV: production
    PORT: '3010'
    REDIS_HOST: redis-master.coreui.svc.cluster.local
    REDIS_PORT: '6379'
    S3_BUCKET_NAME: nivoda.jewellery.files2
    S3_CATALOG_BUCKET_NAME: nivoda.jewellery.catalog
    S3_REGION: eu-west-2
    S3_SHOPIFY_BUCKET: loupe360.jewelry
    SHOPIFY_REGION: EU
    STULLER_API_KEY: QW50b24uYi5zZWxpbjpXR0NsaXZlOTg=
    STULLER_API_URL: https://api.stuller.com/v2
    SLACK_TOKEN: ********************************************************
  environment: coreui
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/jewellery-service
    tag: 1_BR_20250508-1452_d8e751e
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3010
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: jewellery-service
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health_check
      port: 3010
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 100m
      memory: 300Mi
  securityContext: {}
  service:
    port: 3010
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: coreui
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: jewellery-service-coreui.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  enabled: false
