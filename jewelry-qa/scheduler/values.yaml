application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    USE_NETSUITE: "false"
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_API_KEY: "test"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: http://certificates
    DATABASE_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    DB_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: "true"
    FOLLOWER_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    HOST: https://website-jewelry-qa.dev.nivodaapi.net/
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_SQL_LOGGING: "false"
    NODE_ENV: staging
    run_migration: "true"
    PGSSLMODE: no-verify
    PORT: "3000"
    REDIS_TLS_URL: rediss://redis-master.jewelry-qa.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.jewelry-qa.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.jewelry-qa.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-jewelry-qa.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    USE_XERO_DEMO: "true"
    WDC_ENV: staging
    QUEUE_ENABLED: "true"
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: website-jewelry-qa.dev.nivodaapi.net
    pool_max: "500"
    pool_acquire: "30000"
    ALLIANZ_NIVODA_SERVICE: http://allianz
    AIRWALLEX_NIVODA_SERVICE: http://airwallex
    AIRWALLEX_EMAIL: <EMAIL>
    ALLIANZ_POLICY_ID: '291417601'
    idle_in_transaction_session_timeout: "400000"
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_USERNAME: nivoda
    KAFKA_PASSWORD: nivoda123
    KAFKA_ENABLED: "true"
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    DB_CONNECTION_URL: postgresql://postgres:<EMAIL>/d6ruvqcqduhvn5
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    NIVODA_INVOICES_BUCKET: nivoda-staging
    NIVODA_STAGING_BUCKET: nivoda-staging
    OTEL_EXPORTER_OTLP_ENDPOINT: http://tempo.monitoring.svc.cluster.local:4318/v1/traces
    otel_enabled: enabled
    OTEL_TRACES_EXPORTER: otlp
    OTEL_SERVICE_NAME: integrations-jewelry-qa
    CREDIT_SAFE_URL: https://connect.sandbox.creditsafe.com/v1
    CREDIT_SAFE_USERNAME: <EMAIL>
    CREDIT_SAFE_PASSWORD: k!em140V7$45e3i!_;ktM9
  environment: jewelry-qa
  envFrom: 
  - secretRef:
      name: common-secrets
  command: node scheduler.js
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20241224-2007_383c25127
  imagePullSecrets: []
  labels: {}
  name: scheduler
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 100m
      memory: 3000Mi
    requests:
      cpu: 100m
      memory: 3000Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false  