apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: jewelry-qa
  namespace: argocd
spec:
  description: "Project for jewelry-qa environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: jewelry-qa 
  roles:
    - name: admin
      policies:
        - p, proj:jewelry-qa:admin, applications, *, proj:jewelry-qa, allow
        - p, proj:jewelry-qa:admin, clusters, *, *, allow
        - p, proj:jewelry-qa:admin, repositories, *, *, allow
      groups:
        - admins
