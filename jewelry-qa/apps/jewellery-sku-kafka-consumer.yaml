apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: jewellery-sku-kafka-consumer-jewelry-qa
  namespace: argocd
spec:
  syncPolicy:
    automated: 
       prune: true
  sources:
    - repoURL: 'https://bitbucket.org/nivoda/gitops.git'
      targetRevision: dev
      ref: valuesRepo
      helm:
        releaseName: worker-chart
    - repoURL: 'https://bitbucket.org/nivoda/infra-helm-charts.git' 
      path: worker-chart
      targetRevision: v6
      helm:
        releaseName: worker-chart
        valueFiles:
          - $valuesRepo/jewelry-qa/jewellery-sku-kafka-consumer/values.yaml
  destination:
    name: 'in-cluster'
    namespace: 'jewelry-qa'
  project: jewelry-qa