application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 2
    minReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: "https://accounting-service-3197a576c6503f9f.onporter.run"
    ADMIN_DEFAULT_PARTICIPANTS: "4c840f4f-5012-4725-9b11-55b92cabc1e3"
    AIRWALLEX_NIVODA_SERVICE: "https://airwallex-mediaops.nivodaapi.net"
    ALLIANZ_NIVODA_SERVICE: "https://allianz-mediaops.nivodaapi.net"
    ALLIANZ_POLICY_ID: "*********"
    API_GATEWAY: "https://dev-gateway.nivodaapi.net"
    APOLLO_ENGINE_ENABLED: "true"
    CERTIFICATE_HOST: "https://certificates-mediaops.nivodaapi.net"
    CERT_REMOVAL_EVENTS: "true"
    CREDIT_SAFE_CREDENTIALS: "map[password:OXLY66kH-Q(ZIQRFM@Q5 username:<EMAIL>]"
    DATABASE_URL: "postgresql://integrations:<EMAIL>/mediaops"
    DB_CONNECTION_URL: "postgresql://integrations:<EMAIL>/mediaops"
    DB_URL: "postgresql://integrations:<EMAIL>/mediaops"
    DIAMONDS_SERVICE_STAGING: https://diamonds-mediaops.nivodaapi.net"
    ELASTIC_NODE: "https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com"
    ENABLE_EMAIL_TRANSLATIONS: "true"
    ENCRYPTION_KEY: "774B191AF2D84AE6B802D2AC49AA9BFF"
    FOLLOWER_URL: "postgresql://integrations:<EMAIL>/mediaops"
    GEMSTONES_SERVICE_STAGING: "https://gemstonesprocessor-mediaops.nivodaapi.net"
    HOST: "https://website-mediaops.nivodaapi.net/"
    KAFKAJS_NO_PARTITIONER_WARNING: "1"
    KAFKA_AUTHORIZATION_IDENTITY: "integrations"
    KAFKA_BROKER: "b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096"
    KAFKA_ENABLED: "false"
    KAFKA_GROUP_ID: "integrations-app"
    KAFKA_HIGH_PRIORITY_EVENT: "HighPriorityEvents"
    KAFKA_MAIN_EVENT: "NivodaEvents"
    KAFKA_PASSWORD: "nivoda123"
    KAFKA_SASL_MECHANISM: "SCRAM-SHA-512"
    KAFKA_SSL: "enabled"
    KAFKA_URGENT_PRIORITY_EVENT: "UrgentNivodaEvents"
    KAFKA_USERNAME: "nivoda"
    KEYCLOAK_ADMIN_SERVER_URL: "https://keycloak-mediaops.nivodaapi.net/auth"
    KEYCLOAK_SERVER_URL: "https://keycloak-mediaops.nivodaapi.net/auth"
    MARKET_PAY_NIVODA_SERVICE: "https://marketpay-mediaops.nivodaapi.net"
    MARKET_PAY_SERVICE_TOKEN: "jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c="
    NIVODA_ADMIN_ID: "c99a63b9-6b2f-4585-928e-7e7b6c067902"
    NIVODA_COMPANY_ID: "3be11e6e-27a6-44fa-bf16-2989d86c8920"
    NIVODA_CONSIGNMENT_BUCKET: "dev-nivoda-staging"
    NIVODA_CREDITREPORT_BUCKET: "dev-nivoda-staging"
    NIVODA_EXPORTS_BUCKET: "dev-nivoda-staging"
    NIVODA_EXPRESS_REQUEST_BUCKET: "dev-nivoda-staging"
    NIVODA_IMAGES_BUCKET: "dev-nivoda-staging"
    NIVODA_INSURANCE_BUCKET: "dev-nivoda-staging"
    NIVODA_INVOICES_BUCKET: "dev-nivoda-staging"
    NIVODA_SQL_LOGGING: "false"
    NIVODA_STAGING_BUCKET: "dev-nivoda-staging"
    NIVODA_SUPPLIER_FILES_BUCKET: "dev-nivoda-staging"
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: "dev-nivoda-staging"
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: "dev-nivoda-staging"
    NODE_ENV: "staging"
    OTEL_EXPORTER_OTLP_ENDPOINT: "http://tempo.monitoring.svc.cluster.local:4318/v1/traces"
    OTEL_SERVICE_NAME: "integrations-mediaops"
    OTEL_TRACES_EXPORTER: "otlp"
    PGSSLMODE: "no-verify"
    PORT: "3000"
    REDIS_TLS_URL: "redis://redis-master.mediaops.svc.cluster.local:6379"
    REDIS_URI_BULL: "redis://redis-master.mediaops.svc.cluster.local:6379"
    REDIS_URL: "redis://redis-master.mediaops.svc.cluster.local:6379"
    REDSHIFT_DATABASE: "bi_server"
    REDSHIFT_HOST: "************"
    REDSHIFT_PASSWORD: "ahmed@1KNuB"
    REDSHIFT_PORT: "5439"
    REDSHIFT_USER: "ahmed"
    S3_IMAGE_BUCKET: "dev-nivoda-staging"
    SERVER_HOST: "https://website-mediaops.nivodaapi.net"
    SLACK_OUTGOING: "*****************************************************************************"
    STOCK_REMOVAL_EVENTS: "true"
    TAYLOR_HART_WEBHOOK_URL: "http://demo7656881.mockable.io/taylor"
    TWILIO_RATE_LIMIT_IDENTIFIER: "end_user_phone_number"
    USER_PROFILES_BUCKET: "dev-nivoda-staging"
    USE_NETSUITE: "false"
    USE_XERO_DEMO: "true"
    WDC_ENV: "staging"
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: "true"
    WS_HOST: "website-mediaops.nivodaapi.net"
    idle_in_transaction_session_timeout: "400000"
    otel_enabled: "enabled"
    pool_acquire: "30000"
    pool_max: "500"
  environment: mediaops
  envFrom: 
  - secretRef:
      name: common-secrets
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: latest
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: integrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 1
  resources:
    limits:
      cpu: 300m
      memory: 3000Mi
    requests:
      cpu: 300m
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: mediaops
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: integrations-mediaops.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: true
