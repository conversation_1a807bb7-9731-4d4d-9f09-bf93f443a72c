apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: mediaops
  namespace: argocd
spec:
  description: "Project for mediaops environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: mediaops 
  roles:
    - name: admin
      policies:
        - p, proj:mediaops:admin, applications, *, proj:mediaops, allow
        - p, proj:mediaops:admin, clusters, *, *, allow
        - p, proj:mediaops:admin, repositories, *, *, allow
      groups:
        - admins
