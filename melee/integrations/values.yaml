application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  args:
  - --require
  - ./instrumentation.js
  - app.js
  autoscaling:
    maxReplicas: 2
    minReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_API_KEY: test
    APOLLO_ENGINE_ENABLED: 'true'
    AUTH_URL: https://website-melee.dev.nivodaapi.net
    CERTIFICATE_HOST: http://certificates
    CONVERT_API_SECRET: lKXs7jqHKWg54qmM
    DATABASE_URL: postgresql://integrations:<EMAIL>/melee_old
    DB_URL: postgresql://integrations:<EMAIL>/melee_old
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/melee_old
    HOST: https://website-melee.dev.nivodaapi.net
    JWT_SECRET: melee-secret
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: melee-integrations-kafka-env-consumer
    KAFKA_HIGH_PRIORITY_EVENT: MeleeEnvNivodaEvents
    KAFKA_MAIN_EVENT: MeleeEnvNivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: MeleeEnvNivodaEvents
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_CONSIGNMENT_BUCKET: dev-nivoda-staging
    NIVODA_CREDITREPORT_BUCKET: dev-nivoda-staging
    NIVODA_EXPORTS_BUCKET: dev-nivoda-staging
    NIVODA_EXPRESS_REQUEST_BUCKET: dev-nivoda-staging
    NIVODA_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_INSURANCE_BUCKET: dev-nivoda-staging
    NIVODA_INVOICES_BUCKET: dev-nivoda-staging
    NIVODA_SQL_LOGGING: 'false'
    NIVODA_STAGING_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_FILES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_ORDER_REQUEST_IMAGES_BUCKET: dev-nivoda-staging
    NIVODA_SUPPLIER_PRICE_RANK_BUCKET: dev-nivoda-staging
    NODE_ENV: production
    NPM_TOKEN: ****************************************
    OTEL_EXPORTER_OTLP_ENDPOINT: http://signoz-otel-collector.signoz:4318/v1/traces
    OTEL_RESOURCE_ATTRIBUTES: service.name=integrations-coreui,service.version=1.0.0,deployment.environment=coreui
    OTEL_SERVICE_NAME: integrations-melee
    OTEL_SERVICE_VERSION: 1.0.0
    OTEL_TRACES_EXPORTER: otlp
    PGSSLMODE: no-verify
    PORT: '3000'
    POSTMEN_KEY: 7d3e527f-cfbd-4e07-99db-88d10053f08e
    POSTMEN_TEST_KEY: c9bc93a5-7934-481b-ab1c-37b262363892
    REACT_APP_GOOGLE_RECAPTCHA: 6LcKmywmAAAAABBX0bnfL0IeznCOazdLQz9xBULA
    REDIS_TLS_URL: rediss://redis-master.melee.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.melee.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.melee.svc.cluster.local:6379
    S3_IMAGE_BUCKET: dev-nivoda-staging
    SERVER_HOST: https://website-melee.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    SONARQUBE_SERVER_URL: http://*************:9000
    SONARQUBE_TOKEN: ****************************************
    SQS_QUEUE_URL: https://sqs.us-east-2.amazonaws.com/242555748887/nivoda-demo.fifo
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_SID: **********************************
    TWILIO_TOKEN: 3ec4e8a4cad679bb3545b5adbe782e9c
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USER_PROFILES_BUCKET: dev-nivoda-staging
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: https://website-melee.dev.nivodaapi.net
    idle_in_transaction_session_timeout: '400000'
    otel_enabled: enabled
    pool_acquire: '30000'
    pool_max: '500'
  envFrom:
  - secretRef:
      name: common-secrets
  environment: melee
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250728-0543_16723a44ad
  imagePullSecrets: []
  labels: {}
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  name: integrations
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /graphql?query=%7B__typename%7D
      port: 3000
    initialDelaySeconds: 60
    periodSeconds: 60
    successThreshold: 1
    timeoutSeconds: 60
  replicaCount: 2
  resources:
    limits:
      cpu: 300m
      memory: 3000Mi
    requests:
      cpu: 300m
      memory: 3000Mi
  securityContext: {}
  service:
    port: 3000
    type: ClusterIP
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
ingress:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:************:certificate/1fe4e895-2000-401c-8740-f36d934193b4
    alb.ingress.kubernetes.io/group.name: melee
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: alb
  enabled: true
  hosts:
  - host: integrations-melee.dev.nivodaapi.net
    path: /
    pathType: Prefix
    port: 80
presync:
  args:
  - migrate.js
  command:
  - node
  enabled: false
