deployment:
  kong:
    enabled: true
  serviceAccount:
    create: true
  #  name:
    annotations: {}

  
 
  test:
    # Enable creation of test resources for use with "helm test"
    enabled: false
  # Use a DaemonSet controller instead of a Deployment controller
  daemonset: false
  hostNetwork: false


env:
  database: "off"
  # plugins: bundled,opentelemetry
  tracing: "opentelemetry"
  tracing_instrumentations: all
  nginx_worker_processes: "2"
  proxy_access_log: /dev/stdout
  admin_access_log: /dev/stdout
  admin_gui_access_log: /dev/stdout
  portal_api_access_log: /dev/stdout
  proxy_error_log: /dev/stderr
  admin_error_log: /dev/stderr
  admin_gui_error_log: /dev/stderr
  portal_api_error_log: /dev/stderr
  prefix: /kong_prefix/

# This section can be used to configure some extra labels that will be added to each Kubernetes object generated.
extraLabels: {}

# Specify Kong's Docker image and repository details here
image:
  repository: kong/kong-gateway
  tag: "3.9"
  # Kong Enterprise
  # repository: kong/kong-gateway
  # tag: "*******-alpine"

  pullPolicy: IfNotPresent
 

# Specify Kong admin API service and listener configuration
admin:

  enabled: true
  type: NodePort
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}

  http:
    # Enable plaintext HTTP listen for the admin API
    # Disabling this and using a TLS listen only is recommended for most configuration
    enabled: true
    servicePort: 8001
    containerPort: 8001
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32080
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters: []

  tls:
    # Enable HTTPS listen for the admin API
    enabled: true
    servicePort: 8444
    containerPort: 8444
    # Set a target port for the TLS port in the admin API service, useful when using TLS
    # termination on an ELB.
    # overrideServiceTargetPort: 8000
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32443
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters:
    - http2

  # Kong admin ingress settings. Useful if you want to expose the Admin
  # API of Kong outside the k8s cluster.
  ingress:
    # Enable/disable exposure using ingress.
    enabled: false
    ingressClassName:
    # TLS secret name.
    # tls: kong-admin.example.com-tls
    # Ingress hostname
    hostname:
    # Map of ingress annotations.
    annotations: {}
    # Ingress path.
    path: /

# Specify Kong status listener configuration
# This listen is internal-only. It cannot be exposed through a service or ingress.
status:
  enabled: true
  http:
    # Enable plaintext HTTP listen for the status listen
    enabled: true
    containerPort: 8100
    parameters: []

  tls:
    # Enable HTTPS listen for the status listen
    # Kong versions prior to 2.1 do not support TLS status listens.
    # This setting must remain false on those versions
    enabled: false
    containerPort: 8543
    parameters: []


cluster:
  enabled: false
  annotations: {}
  labels: {}

  tls:
    enabled: false
    servicePort: 8005
    containerPort: 8005
    parameters: []

  type: ClusterIP

# Specify Kong proxy service configuration
proxy:
  # Enable creating a Kubernetes service for the proxy
  enabled: true
  type: NodePort
  annotations: {}
  labels:
    enable-metrics: "true"

  http:
    # Enable plaintext HTTP listen for the proxy
    enabled: true
    servicePort: 80
    containerPort: 8000
    parameters: []

  tls:
    # Enable HTTPS listen for the proxy
    enabled: false
    servicePort: 443
    containerPort: 8443
   
    parameters:
    - http2


  stream: {}
 
  ingress:
    # Enable/disable exposure using ingress.
    enabled: true
    ingressClassName:
    # Ingress hostname
    # TLS secret name.
    # tls: kong-admin.example.com-tls
    hostname: kong-melee.dev.nivodaapi.net
    # Map of ingress annotations.
    annotations: 
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-west-2:633477234672:certificate/1fe4e895-2000-401c-8740-f36d934193b4
      alb.ingress.kubernetes.io/group.name: melee
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
      alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=4000
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/target-type: ip
      kubernetes.io/ingress.class: alb
    # Ingress path.
    path: /
    pathType: Prefix



udpProxy:
  # Enable creating a Kubernetes service for UDP proxying
  enabled: false
  type: LoadBalancer
 
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}
  # Optionally specify a static load balancer IP.
  # loadBalancerIP:

  # Define stream (UDP) listen
  # To enable, remove "{}", uncomment the section below, and select your desired
  # ports and parameters. Listens are dynamically named after their servicePort,
  # e.g. "stream-9000" for the below.
  stream: {}
  
plugins: {}
 
secretVolumes: []

# Enable/disable migration jobs, and set annotations for them
migrations:
  # Enable pre-upgrade migrations (run "kong migrations up")
  preUpgrade: true
  # Enable post-upgrade migrations (run "kong migrations finish")
  postUpgrade: true
  # Annotations to apply to migrations job pods
  # By default, these disable service mesh sidecar injection for Istio and Kuma,
  # as the sidecar containers do not terminate and prevent the jobs from completing
  annotations:
    sidecar.istio.io/inject: false
  # Additional annotations to apply to migration jobs
  # This is helpful in certain non-Helm installation situations such as GitOps
  # where additional control is required around this job creation.
  jobAnnotations: {}
  # Optionally set a backoffLimit. If none is set, Jobs will use the cluster default
  backoffLimit:
  resources: {}
 
dblessConfig:
  # Either Kong's configuration is managed from an existing ConfigMap (with Key: kong.yml)
  configMap: ""
  # Or the configuration is passed in full-text below
  config: |
    _format_version: "2.1"
    _transform: true
    services:
      - connect_timeout: 60000
        enabled: true
        host: integrations
        name: mobile_monolith
        port: 80
        protocol: http
        read_timeout: 360000
        retries: 5
        routes:
        - https_redirect_status_code: 426
          name: protected_routes_mobile
          path_handling: v0
          paths:
          - /graphql-mobile
          plugins:
          - config:
              add:
                body: []
                headers: []
                querystring: []
              append:
                body: []
                headers: []
                querystring: []
              http_method: null
              remove:
                body: []
                headers: []
                querystring: []
              rename:
                body: []
                headers: []
                querystring: []
              replace:
                body: []
                headers: []
                querystring: []
                uri: /graphql
            enabled: true
            name: request-transformer
            protocols:
            - grpc
            - grpcs
            - http
            - https
          preserve_host: false
          protocols:
          - http
          - https
          regex_priority: 0
          request_buffering: true
          response_buffering: true
          strip_path: false
        - https_redirect_status_code: 426
          name: protected_routes_mobile_supplier
          path_handling: v0
          paths:
          - /graphql-mobile-supplier
          plugins:
          - config:
              add:
                body: []
                headers: []
                querystring: []
              append:
                body: []
                headers: []
                querystring: []
              http_method: null
              remove:
                body: []
                headers: []
                querystring: []
              rename:
                body: []
                headers: []
                querystring: []
              replace:
                body: []
                headers: []
                querystring: []
                uri: /graphql-supplier
            enabled: true
            name: request-transformer
            protocols:
            - grpc
            - grpcs
            - http
            - https
          preserve_host: false
          protocols:
          - http
          - https
          regex_priority: 0
          request_buffering: true
          response_buffering: true
          strip_path: false
        - https_redirect_status_code: 426
          name: public_routes_mobile
          path_handling: v0
          paths:
          - /graphql-public-mobile
          plugins:
          - config:
              add:
                body: []
                headers: []
                querystring: []
              append:
                body: []
                headers: []
                querystring: []
              http_method: null
              remove:
                body: []
                headers: []
                querystring: []
              rename:
                body: []
                headers: []
                querystring: []
              replace:
                body: []
                headers: []
                querystring: []
                uri: /graphql-public
            enabled: true
            name: request-transformer
            protocols:
            - grpc
            - grpcs
            - http
            - https
          preserve_host: false
          protocols:
          - http
          - https
          regex_priority: 0
          request_buffering: true
          response_buffering: true
          strip_path: false
      - name: syncintegrations
        connect_timeout: 60000
        enabled: true
        host: syncintegrations
        port: 80
        protocol: http
        read_timeout: 60000
        retries: 5
        write_timeout: 60000
        routes:
        - name: zsync-secured
          path_handling: v0
          paths:
          - /zsync/api
          preserve_host: false
          protocols:
          - http
          - https
          regex_priority: 0
          request_buffering: true
          response_buffering: true
          strip_path: false
          plugins:
          - name: key-auth
            enabled: true
            config:
              anonymous: null
              hide_credentials: false
              key_in_body: true
              key_in_header: true
              key_in_query: true
              key_names:
              - apikey
              run_on_preflight: false

      - name: translations
        host: translations
        port: 80
        protocol: http
        routes:
          - name: public_routes_translations
            paths:
              - /graphql-translation
            strip_path: false

      - name: market_pay
        host: marketpay
        port: 80
        protocol: http
        routes:
          - name: public_routes_market_pay
            paths:
              - /api/v1/market_pay
              - /api/v2/market_pay
            strip_path: false
 
      - name: jewellery_service
        host: jewellery-service
        port: 80
        protocol: http
        routes:

          - name: protected_routes_jewellery_service
            paths:
              - /jewellery-service-api
            strip_path: true
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400
 
      - name: feeds_api
        host: feedsapi
        port: 80
        protocol: http
        routes:
          - name: public_routes_feeds_api
            paths:
              - /feeds-api
            methods:
              - GET
              - OPTIONS
            strip_path: false
            plugins:
              - name: cors
                config:
                  origins:
                    - "*"
                  methods:
                    - GET
                    - OPTIONS
                  headers:
                    - Authorization
                    - Content-Type
                  exposed_headers:
                    - Authorization
                  credentials: true
                  max_age: 3600
          - name: protected_routes_feeds_api
            paths:
              - /feeds-api
            strip_path: false
            methods:
              - PUT
              - POST
              - DELETE
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400

      - name: api_gateway
        host: apigateway
        port: 80
        protocol: http
        routes:
          - name: protected_routes_gateway_key
            paths:
              - /feature_flagging/isAllowedFor
            strip_path: false
            plugins:
              - name: key-auth
                enabled: true
                config:
                  anonymous: null
                  hide_credentials: false
                  key_in_body: true
                  key_in_header: true
                  key_in_query: true
                  key_names:
                  - apikey
                  - token                  
                  run_on_preflight: false

          - name: protected_routes_gateway
            paths:
              - /feature_flagging/isAllowed
              - /keycloak
            strip_path: false
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400

          - name: public_routes_feature
            paths:
              - /feature_flagging/V2Company
            strip_path: false

      - name: main_monolith
        host: integrations
        port: 80
        protocol: http
        routes:
          - name: public_routes_monolith
            paths:
              - /graphql-public
              - /
            strip_path: false
            plugins:
              - name: opentelemetry
                config:
                  traces_endpoint: http://signoz-otel-collector.signoz:4318/v1/traces
                  logs_endpoint: http://signoz-otel-collector.signoz:4318/v1/logs
                  resource_attributes:
                    service.name: kong-gateway
                    deployment.environment: melee
                  sampling_rate: 1.0
          - name: protected_routes_monolith
            paths:
              - /graphql
              - /graphql-supplier
            strip_path: false
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400
              - name: opentelemetry
                config:
                  traces_endpoint: http://signoz-otel-collector.signoz:4318/v1/traces
                  logs_endpoint: http://signoz-otel-collector.signoz:4318/v1/logs
                  resource_attributes:
                    service.name: kong-gateway
                    deployment.environment: melee
                  sampling_rate: 1.0

      - name: admin_monolith
        host: integrations
        port: 80
        protocol: http
        routes:
          - name: protected_routes_admin
            paths:
              - /graphql-admin
            strip_path: false
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400


      - name: loupe360_monolith
        host: integrations
        port: 80
        protocol: http
        routes:
          - name: protected_routes_loupe360
            paths:
              - /graphql-loupe360
            strip_path: false
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400

      - name: api_monolith
        host: integrations
        port: 80
        protocol: http
        routes:
          - name: protected_routes_api
            paths:
              - /api/diamonds
              - /77diamonds
              - /api/gemstones
              - /api/th/graphql
              - /api/ag
              - /api/jm
              - /api/qs
            strip_path: false
            plugins:
              - name: jwt
                config:
                  uri_param_names:
                    - token
                  key_claim_name: "kid"
                  claims_to_verify:
                    - exp
                  header_names:
                    - authorization
                  secret_is_base64: false
                  run_on_preflight: false
                  maximum_expiration: 86400

          

    consumers:
      - custom_id: apigateway_feature_flagging
        keyauth_credentials:
        - key: gatewaykey123
        username: gateway
      - custom_id: decagon
        keyauth_credentials:
        - key: 21ad0b3e-2952-4771-872c-5746b211c530
        username: decagon
      - username: "keycloak-jwt"
        custom_id: "keycloak-jwt"
        jwt_secrets:
          - key: "jjCSL01wz4Lp0hYiLzzTbjaBSbnWOxLlbaRTMY2kIAo"
            algorithm: "RS256"
            rsa_public_key: |
              -----BEGIN PUBLIC KEY-----
              MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAogZMHwjmaXZCU71bVvuH
              elVjF1I60ujB8wRKBQrSyy0VcreyLOaMV8FIuSU5Ktk2smO97rq0xL6WAJFlsvZq
              Ru7bWy4mS2FfqSLtGrApnFUrxHmN9z9eymGD9dVPJmiVmO6RoNqq4UP+FFP0wEwZ
              erN8pKREGrBs99i/L8IczPaZSyh+Tp/orz/Re/erLqV5y8k+BuAtKhHNOst9bU3X
              izX77gFkjD00JdIcF9NWlMaB7KVaN9QNa0NaXPSIRIXhYrsUSlpvrnEN3+fVnAWI
              J8guRawN5PwRre3WE3Ba/Xrc7KG3EP0Kn6RBuXN3ZUkKFcu/64AVkQ4+nylHyeUk
              twIDAQAB
              -----END PUBLIC KEY-----
          - key: "f58790d17"
            algorithm: "HS256"
            secret: secretjaleukoke



ingressController:
  enabled: false
  image:
    repository: kong/kubernetes-ingress-controller
    tag: "2.1"
  
    effectiveSemver:
  args: []


  watchNamespaces: []

  # Specify Kong Ingress Controller configuration via environment variables
  env:
    # The controller disables TLS verification by default because Kong
    # generates self-signed certificates by default. Set this to false once you
    # have installed CA-signed certificates.
    kong_admin_tls_skip_verify: true
 
  admissionWebhook:
    enabled: false
    failurePolicy: Fail
    port: 8080
    certificate:
      provided: false
  

  ingressClass: kong
  # annotations for IngressClass resource (Kubernetes 1.18+)
  ingressClassAnnotations: {}

  rbac:
    # Specifies whether RBAC resources should be created
    create: true

  # general properties
  livenessProbe:
    httpGet:
      path: "/healthz"
      port: 10254
      scheme: HTTP
    initialDelaySeconds: 5
    timeoutSeconds: 5
    periodSeconds: 10
    successThreshold: 1
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: "/healthz"
      port: 10254
      scheme: HTTP
    initialDelaySeconds: 5
    timeoutSeconds: 5
    periodSeconds: 10
    successThreshold: 1
    failureThreshold: 3
  resources: {}
  

postgresql:
  enabled: false
  # postgresqlUsername: kong
  # postgresqlDatabase: kong
  # service:
  #   port: 5432

# -----------------------------------------------------------------------------
# Miscellaneous parameters
# -----------------------------------------------------------------------------

waitImage:
  # Wait for the database to come online before starting Kong or running migrations
  # If Kong is to access the database through a service mesh that injects a sidecar to
  # Kong's container, this must be disabled. Otherwise there'll be a deadlock:
  # InitContainer waiting for DB access that requires the sidecar, and the sidecar
  # waiting for InitContainers to finish.
  enabled: true
  # Optionally specify an image that provides bash for pre-migration database
  # checks. If none is specified, the chart uses the Kong image. The official
  # Kong images provide bash
  # repository: bash
  # tag: 5
  pullPolicy: IfNotPresent

# update strategy
updateStrategy: {}
  # type: RollingUpdate
  # rollingUpdate:
  #   maxSurge: "100%"
  #   maxUnavailable: "0%"

# If you want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
resources: {}
  # limits:
  #  cpu: 1
  #  memory: 1G
  # requests:
  #  cpu: 1
  #  memory: 1G

# readinessProbe for Kong pods
readinessProbe:
  httpGet:
    path: "/status"
    port: status
    scheme: HTTP
  initialDelaySeconds: 5
  timeoutSeconds: 5
  periodSeconds: 10
  successThreshold: 1
  failureThreshold: 3

# livenessProbe for Kong pods
livenessProbe:
  httpGet:
    path: "/status"
    port: status
    scheme: HTTP
  initialDelaySeconds: 5
  timeoutSeconds: 5
  periodSeconds: 10
  successThreshold: 1
  failureThreshold: 3

# Proxy container lifecycle hooks
# Ref: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/
lifecycle:
  preStop:
    exec:
      # Note kong quit has a default timeout of 10 seconds
      command: ["/bin/sh", "-c", "/bin/sleep 15 && kong quit"]

# Sets the termination grace period for pods spawned by the Kubernetes Deployment.
# Ref: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/#hook-handler-execution
terminationGracePeriodSeconds: 30

# Affinity for pod assignment
# Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
# affinity: {}

# Topology spread constraints for pod assignment (requires Kubernetes >= 1.19)
# Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
# topologySpreadConstraints: []

# Tolerations for pod assignment
# Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations: []

# Node labels for pod assignment
# Ref: https://kubernetes.io/docs/user-guide/node-selection/
nodeSelector: {}

# Annotation to be added to Kong pods
podAnnotations: 
  kuma.io/gateway: enabled
  traffic.sidecar.istio.io/includeInboundPorts: ""

# Labels to be added to Kong pods
podLabels: {}

# Kong pod count.
# It has no effect when autoscaling.enabled is set to true
replicaCount: 1

# Annotations to be added to Kong deployment
deploymentAnnotations: {}

# Enable autoscaling using HorizontalPodAutoscaler
# When configuring an HPA, you must set resource requests on all containers via
# "resources" and, if using the controller, "ingressController.resources" in values.yaml
autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 5
  ## targetCPUUtilizationPercentage only used if the cluster doesn't support autoscaling/v2beta
  targetCPUUtilizationPercentage:
  ## Otherwise for clusters that do support autoscaling/v2beta, use metrics
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80

# Kong Pod Disruption Budget
podDisruptionBudget:
  enabled: false
  # Uncomment only one of the following when enabled is set to true
  # maxUnavailable: "50%"
  # minAvailable: "50%"

podSecurityPolicy:
  enabled: false
  spec:
    privileged: false
    fsGroup:
      rule: RunAsAny
    runAsUser:
      rule: RunAsAny
    runAsGroup:
      rule: RunAsAny
    seLinux:
      rule: RunAsAny
    supplementalGroups:
      rule: RunAsAny
    volumes:
      - 'configMap'
      - 'secret'
      - 'emptyDir'
    allowPrivilegeEscalation: false
    hostNetwork: false
    hostIPC: false
    hostPID: false
    # Make the root filesystem read-only. This is not compatible with Kong Enterprise <1.5.
    # If you use Kong Enterprise <1.5, this must be set to false.
    readOnlyRootFilesystem: true


priorityClassName: ""

# securityContext for Kong pods.
securityContext: {}

# securityContext for containers.
containerSecurityContext: {}

## Optional DNS configuration for Kong pods
# dnsPolicy: ClusterFirst
# dnsConfig:
#   nameservers:
#   - "***********"
#   options:
#   - name: ndots
#     value: "5"
#   searches:
#   - default.svc.cluster.local
#   - svc.cluster.local
#   - cluster.local
#   - us-east-1.compute.internal

serviceMonitor:
  # Specifies whether ServiceMonitor for Prometheus operator should be created
  # If you wish to gather metrics from a Kong instance with the proxy disabled (such as a hybrid control plane), see:
  # https://github.com/Kong/charts/blob/main/charts/kong/README.md#prometheus-operator-integration
  enabled: false
  # interval: 10s
  # Specifies namespace, where ServiceMonitor should be installed
  # namespace: monitoring
  # labels:
  #   foo: bar
  # targetLabels:
  #   - foo

  # honorLabels: false
  # metricRelabelings: []

# -----------------------------------------------------------------------------
# Kong Enterprise parameters
# -----------------------------------------------------------------------------

# Toggle Kong Enterprise features on or off
# RBAC and SMTP configuration have additional options that must all be set together
# Other settings should be added to the "env" settings below
enterprise:
  enabled: false
  # Kong Enterprise license secret name
  # This secret must contain a single 'license' key, containing your base64-encoded license data
  # The license secret is required to unlock all Enterprise features. If you omit it,
  # Kong will run in free mode, with some Enterprise features disabled.
  # license_secret: kong-enterprise-license
  vitals:
    enabled: true
  portal:
    enabled: false
  rbac:
    enabled: false
    admin_gui_auth: basic-auth
    # If RBAC is enabled, this Secret must contain an admin_gui_session_conf key
    # The key value must be a secret configuration, following the example at
    # https://docs.konghq.com/enterprise/latest/kong-manager/authentication/sessions
    session_conf_secret: kong-session-config
    # If admin_gui_auth is not set to basic-auth, provide a secret name which
    # has an admin_gui_auth_conf key containing the plugin config JSON
    admin_gui_auth_conf_secret: CHANGEME-admin-gui-auth-conf-secret
  # For configuring emails and SMTP, please read through:
  # https://docs.konghq.com/enterprise/latest/developer-portal/configuration/smtp
  # https://docs.konghq.com/enterprise/latest/kong-manager/networking/email
  smtp:
    enabled: false
    portal_emails_from: <EMAIL>
    portal_emails_reply_to: <EMAIL>
    admin_emails_from: <EMAIL>
    admin_emails_reply_to: <EMAIL>
    smtp_admin_emails: <EMAIL>
    smtp_host: smtp.example.com
    smtp_port: 587
    smtp_auth_type: ''
    smtp_ssl: nil
    smtp_starttls: true
    auth:
      # If your SMTP server does not require authentication, this section can
      # be left as-is. If smtp_username is set to anything other than an empty
      # string, you must create a Secret with an smtp_password key containing
      # your SMTP password and specify its name here.
      smtp_username: ''  # e.g. <EMAIL>
      smtp_password_secret: CHANGEME-smtp-password

manager:
  # Enable creating a Kubernetes service for Kong Manager
  enabled: true
  type: NodePort
  # To specify annotations or labels for the Manager service, add them to the respective
  # "annotations" or "labels" dictionaries below.
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}

  http:
    # Enable plaintext HTTP listen for Kong Manager
    enabled: true
    servicePort: 8002
    containerPort: 8002
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32080
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters: []

  tls:
    # Enable HTTPS listen for Kong Manager
    enabled: true
    servicePort: 8445
    containerPort: 8445
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32443
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters:
    - http2

  ingress:
    # Enable/disable exposure using ingress.
    enabled: false
    ingressClassName:
    # TLS secret name.
    # tls: kong-proxy.example.com-tls
    # Ingress hostname
    hostname:
    # Map of ingress annotations.
    annotations: {}
    # Ingress path.
    path: /

portal:
  # Enable creating a Kubernetes service for the Developer Portal
  enabled: true
  type: NodePort
  # To specify annotations or labels for the Portal service, add them to the respective
  # "annotations" or "labels" dictionaries below.
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}

  http:
    # Enable plaintext HTTP listen for the Developer Portal
    enabled: true
    servicePort: 8003
    containerPort: 8003
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32080
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters: []

  tls:
    # Enable HTTPS listen for the Developer Portal
    enabled: true
    servicePort: 8446
    containerPort: 8446
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32443
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters:
    - http2

  ingress:
    # Enable/disable exposure using ingress.
    enabled: false
    ingressClassName:
    # TLS secret name.
    # tls: kong-proxy.example.com-tls
    # Ingress hostname
    hostname:
    # Map of ingress annotations.
    annotations: {}
    # Ingress path.
    path: /

portalapi:
  # Enable creating a Kubernetes service for the Developer Portal API
  enabled: true
  type: NodePort
  # To specify annotations or labels for the Portal API service, add them to the respective
  # "annotations" or "labels" dictionaries below.
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}

  http:
    # Enable plaintext HTTP listen for the Developer Portal API
    enabled: true
    servicePort: 8004
    containerPort: 8004
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32080
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters: []

  tls:
    # Enable HTTPS listen for the Developer Portal API
    enabled: true
    servicePort: 8447
    containerPort: 8447
    # Set a nodePort which is available if service type is NodePort
    # nodePort: 32443
    # Additional listen parameters, e.g. "reuseport", "backlog=16384"
    parameters:
    - http2

  ingress:
    # Enable/disable exposure using ingress.
    enabled: false
    ingressClassName:
    # TLS secret name.
    # tls: kong-proxy.example.com-tls
    # Ingress hostname
    hostname:
    # Map of ingress annotations.
    annotations: {}
    # Ingress path.
    path: /

clustertelemetry:
  enabled: false
  # To specify annotations or labels for the cluster telemetry service, add them to the respective
  # "annotations" or "labels" dictionaries below.
  annotations: {}
  #  service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: "*"
  labels: {}

  tls:
    enabled: false
    servicePort: 8006
    containerPort: 8006
    parameters: []

  type: ClusterIP

extraConfigMaps: []

extraSecrets: []
