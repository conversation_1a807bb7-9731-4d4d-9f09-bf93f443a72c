application:
  affinity: {}
  annotations:
    reloader.stakater.com/auto: 'true'
  autoscaling:
    maxReplicas: 1
    minReplicas: 1
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  command: node observers/Consumer.js
  env:
    ACCOUNTING_NETSUITE_URL: http://accountingservice
    ADMIN_DEFAULT_PARTICIPANTS: 4c840f4f-5012-4725-9b11-55b92cabc1e3
    AIRWALLEX_NIVODA_SERVICE: http://airwallex
    ALLIANZ_NIVODA_SERVICE: http://allianz
    ALLIANZ_POLICY_ID: '*********'
    API_GATEWAY: http://apigateway
    APOLLO_ENGINE_API_KEY: test
    APOLLO_ENGINE_ENABLED: 'true'
    AUTH_URL: https://website-melee.dev.nivodaapi.net
    CERTIFICATE_HOST: http://certificates
    CERT_REMOVAL_EVENTS: 'true'
    CONVERT_API_SECRET: lKXs7jqHKWg54qmM
    CREDIT_SAFE_CREDENTIALS: '{"username": "<EMAIL>","password": "OXLY66kH-Q(ZIQRFM@Q5"}'
    DATABASE_URL: postgresql://integrations:<EMAIL>/melee_old
    DB_CONNECTION_URL: postgresql://integrations:<EMAIL>/melee_old
    DB_URL: postgresql://integrations:<EMAIL>/melee_old
    DIAMONDS_SERVICE_STAGING: http://diamonds
    ELASTIC_NODE: https://vpc-staging-elasticsearch-v7yk444kftvx5girdiflaupu3m.eu-west-2.es.amazonaws.com
    ENABLE_EMAIL_TRANSLATIONS: 'true'
    ENCRYPTION_KEY: 774B191AF2D84AE6B802D2AC49AA9BFF
    FOLLOWER_URL: postgresql://integrations:<EMAIL>/melee_old
    GEMSTONES_SERVICE_STAGING: http://gemstonesprocessor
    HOST: https://website-melee.dev.nivodaapi.net/
    JWT_SECRET: melee-secret
    KAFKAJS_NO_PARTITIONER_WARNING: '1'
    KAFKA_AUTHORIZATION_IDENTITY: integrations
    KAFKA_BROKER: b-3.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-2.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096,b-1.devkafkacluster.dr1bqc.c3.kafka.eu-west-2.amazonaws.com:9096
    KAFKA_ENABLED: 'true'
    KAFKA_GROUP_ID: melee-integrations-kafka-env-consumer
    KAFKA_HIGH_PRIORITY_EVENT: MeleeEnvNivodaEvents
    KAFKA_MAIN_EVENT: MeleeEnvNivodaEvents
    KAFKA_PASSWORD: nivoda123
    KAFKA_SASL_MECHANISM: SCRAM-SHA-512
    KAFKA_SSL: enabled
    KAFKA_TOPIC: MeleeEnvNivodaEvents
    KAFKA_URGENT_PRIORITY_EVENT: UrgentNivodaEvents
    KAFKA_USERNAME: nivoda
    KEYCLOAK_ADMIN_SERVER_URL: http://keycloak/auth
    KEYCLOAK_SERVER_URL: http://keycloak/auth
    MARKET_PAY_NIVODA_SERVICE: http://marketpay
    MARKET_PAY_SERVICE_TOKEN: jse3RxkTuam6SGce2etxTq8RIoGXH/dfQRjTcxtv28c=
    NIVODA_ADMIN_ID: c99a63b9-6b2f-4585-928e-7e7b6c067902
    NIVODA_COMPANY_ID: 3be11e6e-27a6-44fa-bf16-2989d86c8920
    NIVODA_SQL_LOGGING: 'false'
    NODE_ENV: production
    NPM_TOKEN: ****************************************
    PGSSLMODE: no-verify
    PORT: '3000'
    POSTMEN_KEY: 7d3e527f-cfbd-4e07-99db-88d10053f08e
    POSTMEN_TEST_KEY: c9bc93a5-7934-481b-ab1c-37b262363892
    REACT_APP_GOOGLE_RECAPTCHA: 6LcKmywmAAAAABBX0bnfL0IeznCOazdLQz9xBULA
    REDIS_TLS_URL: rediss://redis-master.melee.svc.cluster.local:6379
    REDIS_URI_BULL: redis://redis-master.melee.svc.cluster.local:6379
    REDIS_URL: redis://redis-master.melee.svc.cluster.local:6379
    S3_IMAGE_BUCKET: nivoda-staging
    SERVER_HOST: https://website-melee.dev.nivodaapi.net
    SLACK_OUTGOING: *****************************************************************************
    SONARQUBE_SERVER_URL: http://*************:9000
    SONARQUBE_TOKEN: ****************************************
    SQS_QUEUE_URL: https://sqs.us-east-2.amazonaws.com/242555748887/nivoda-demo.fifo
    STOCK_REMOVAL_EVENTS: 'true'
    TAYLOR_HART_WEBHOOK_URL: http://demo7656881.mockable.io/taylor
    TWILIO_CONTENT_SID: HXe2f26f483d2c289067c5cab49d110637
    TWILIO_RATE_LIMIT_IDENTIFIER: end_user_phone_number
    TWILIO_SID: AC7a2f726ccded8b8a0e151b6c4700791d
    TWILIO_TOKEN: melee-twilio-token
    TWILIO_WHATSAPP_NUMBER: whatsapp:+19787889489
    USE_NETSUITE: 'false'
    USE_XERO_DEMO: 'true'
    WDC_ENV: staging
    WDC_PLEASE_TURN_OFF_SQL_LOGGING_GODDAMN: 'true'
    WS_HOST: website-melee.dev.nivodaapi.net
    XERO_APP_CKCS: 40B7A9067390401780238281A583E648
    XERO_APP_CS: eEkFieJuAmtpMlMiixt1BylqPK9QEseKC8K6m_POp95Qx-bm
    XERO_CLIENT_ID: AAF6FE634D7B43D1A8BA896DAE651FAB
    XERO_CLIENT_SECRET: fL9LjedtoRJV499uREp-HGR3Kx5gUW8zaqkVA3bn6cT_U_50
    idle_in_transaction_session_timeout: '400000'
    pool_acquire: '30000'
    pool_max: '500'
  environment: melee
  image:
    pullPolicy: IfNotPresent
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/integrations
    tag: 1_BR_20250728-0543_16723a44ad
  imagePullSecrets: []
  labels: {}
  name: kafkamainintegrationsworker
  nodeSelector: {}
  podAnnotations: {}
  podSecurityContext: {}
  replicaCount: 1
  resources:
    limits:
      cpu: 300m
      memory: 1500Mi
    requests:
      cpu: 200m
      memory: 1000Mi
  securityContext: {}
  serviceaccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-role
  tolerations: []
  volumeMounts: []
  volumes: []
scaledObject:
  enabled: false
