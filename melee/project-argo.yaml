apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: melee
  namespace: argocd
spec:
  description: "Project for melee environment"
  sourceRepos:
    - '*'
  destinations:
    - server: 'https://kubernetes.default.svc'
      namespace: melee 
  roles:
    - name: admin
      policies:
        - p, proj:melee:admin, applications, *, proj:melee, allow
        - p, proj:melee:admin, clusters, *, *, allow
        - p, proj:melee:admin, repositories, *, *, allow
      groups:
        - admins
